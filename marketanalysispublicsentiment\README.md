# Financial Sentiment Analyzer with Government Policy Integration

A comprehensive Python tool for analyzing market sentiment from news sources and government policy announcements to provide actionable trading insights.

## 🚀 Features

### Core Functionality
- **Market Sentiment Analysis**: Analyzes news from 80+ major stock tickers
- **Government Policy Analysis**: Monitors Federal Reserve and regulatory announcements
- **Combined Analysis**: Integrates market and policy sentiment for comprehensive insights
- **Real-time Data**: Fetches live news and market data
- **Parallel Processing**: Efficient data fetching using concurrent processing
- **Modular Design**: Clean separation of concerns with dedicated modules

### Analysis Capabilities
- **Ticker Sentiment Rankings**: Individual stock sentiment scores and rankings
- **Sector Performance**: Sector-wise sentiment analysis and performance metrics
- **Policy Impact Classification**: Automatic categorization of policy news by market impact
- **Analyst Recommendations**: Integration with analyst price targets and recommendations
- **Market Health Assessment**: Overall market trend analysis with policy considerations
- **News Timeline**: Recent news with timestamps and clickable links

## 📁 Project Structure

```text
trading_tools/
├── financial_analyzer.py          # Main entry point with CLI interface
├── config.py                      # Configuration, constants, and settings
├── data_fetcher.py                # News and market data fetching
├── sentiment_analyzer.py          # Market sentiment analysis functions
├── policy_analyzer.py             # Government policy analysis functions
├── display_utils.py               # Output formatting and display utilities
├── README.md                      # This documentation
└── GOVERNMENT_POLICY_INTEGRATION.md  # Policy integration details
```

## 🛠️ Installation

### Prerequisites
- Python 3.7 or higher
- Internet connection for data fetching

### Dependencies
```bash
pip install yfinance textblob numpy feedparser pytz
```

### Quick Setup
```bash
git clone <repository-url>
cd trading_tools
pip install -r requirements.txt  # If available, or install dependencies manually
```

## 📖 Usage

### Basic Usage
```bash
# Full analysis (default)
python financial_analyzer.py

# Show help
python financial_analyzer.py --help
```

### Command Line Options

| Option | Description |
|--------|-------------|
| `--help`, `-h` | Show help message and exit |
| `--market-only` | Run only market sentiment analysis |
| `--policy-only` | Run only government policy analysis |
| `--sectors` | Show detailed sector analysis |
| `--tickers` | Show detailed ticker rankings |
| `--recommendations` | Show analyst recommendations |
| `--indices` | Show market indices performance |
| `--timeline` | Show recent news timeline |
| `--quick` | Quick analysis (fewer sources, faster) |
| `--verbose` | Verbose output with debug information |

### Usage Examples

```bash
# Market sentiment only
python financial_analyzer.py --market-only

# Policy analysis only
python financial_analyzer.py --policy-only

# Quick analysis for faster results
python financial_analyzer.py --quick

# Detailed sector and ticker analysis
python financial_analyzer.py --sectors --tickers

# Show only market indices and recent news
python financial_analyzer.py --indices --timeline
```

## 📊 Output Sections

### 1. Market Sentiment Analysis
- Overall market sentiment score and mood
- Percentage breakdown (positive/negative/neutral)
- Total articles analyzed

### 2. Government Policy Analysis
- Policy sentiment score and assessment
- Policy categories breakdown (monetary policy, regulatory, etc.)
- Total policy articles processed

### 3. High Impact Policy News
- Top policy announcements with market impact scores
- Sentiment analysis of policy changes
- Clickable links to original sources

### 4. Sector Performance
- Top 5 performing sectors by sentiment
- Sector strength scores and metrics
- Best performing ticker in each sector

### 5. Ticker Rankings
- **Best Sentiment Tickers**: Top 5 stocks with positive sentiment
- **Tickers to Watch**: Stocks with negative sentiment
- Analyst recommendations and price targets
- Recent news headlines with timestamps

### 6. Combined Analysis
- Integrated market and policy sentiment scores
- Policy influence assessment
- Overall market trend analysis

### 7. Trading Recommendation
- Buy/Sell/Hold recommendation based on combined analysis
- Market trend classification
- Policy influence indicators

### 8. Market Indices Performance
- Major market indices price changes
- S&P 500, NASDAQ, Dow Jones, Russell 2000 performance

### 9. Recent News Timeline
- Latest news articles with timestamps
- Clickable headlines linking to sources
- Publication times in CDT timezone

## 🏛️ Government Data Sources

### Federal Reserve Feeds
- **Press Releases**: All Federal Reserve announcements
- **Monetary Policy**: FOMC statements and policy decisions
- **Speeches & Testimony**: Fed officials' public statements
- **Banking Regulation**: Regulatory updates and changes
- **Enforcement Actions**: Regulatory enforcement news

### Policy Impact Classification
- **High Impact**: Interest rates, monetary policy, economic outlook
- **Medium Impact**: Banking regulation, financial stability measures
- **Sector Specific**: Industry-specific policies and regulations

## 📈 Market Data Sources

### Stock Tickers (80+ symbols)
- **Technology**: AAPL, MSFT, GOOGL, AMZN, TSLA, META, NVDA, etc.
- **Financial**: JPM, BAC, WFC, GS, MS, V, MA, etc.
- **Healthcare**: JNJ, UNH, PFE, ABT, TMO, etc.
- **Consumer**: WMT, HD, PG, KO, MCD, etc.
- **Industrial**: BA, CAT, GE, MMM, etc.
- **Energy**: XOM, CVX, COP, etc.
- **ETFs**: SPY, QQQ, IWM, DIA, VTI, etc.

### Data Sources
- **Yahoo Finance**: Stock news, prices, analyst recommendations
- **Federal Reserve**: Government policy announcements
- **Real-time Processing**: Parallel fetching for efficiency

## ⚙️ Configuration

### Analysis Settings (config.py)
```python
ANALYSIS_CONFIG = {
    'max_workers': 10,              # Parallel processing workers
    'articles_per_ticker': 3,       # Articles per stock ticker
    'articles_per_feed': 5,         # Articles per government feed
    'market_data_days': 30,         # Days of market data
    'sentiment_thresholds': {       # Sentiment classification
        'positive': 0.1,
        'negative': -0.1
    },
    'policy_weight': 0.3,           # Policy influence weight
    'market_weight': 0.7            # Market sentiment weight
}
```

### Display Settings
```python
DISPLAY_CONFIG = {
    'top_tickers_count': 5,         # Number of top tickers to show
    'negative_tickers_count': 3,    # Number of negative tickers
    'top_sectors_count': 5,         # Number of top sectors
    'high_impact_articles_count': 3 # High impact policy articles
}
```

## 🔧 Technical Details

### Sentiment Analysis
- **TextBlob**: Natural language processing for sentiment scoring
- **Policy Weighting**: Government news weighted by market impact potential
- **Combined Scoring**: Market sentiment (70%) + Policy sentiment (30%)

### Performance Optimizations
- **Parallel Processing**: Concurrent news fetching using ThreadPoolExecutor
- **Caching**: LRU cache for sector mappings and repeated calculations
- **Efficient Data Structures**: Optimized for large-scale data processing

### Error Handling
- **Graceful Degradation**: Continues operation if some data sources fail
- **Fallback Data**: Sample data when live sources are unavailable
- **Robust Parsing**: Handles various date formats and missing data

## 🚨 Troubleshooting

### Common Issues

1. **No news data available**
   - Check internet connection
   - Yahoo Finance API may be temporarily unavailable
   - Use `--quick` flag for reduced data requirements

2. **Government feeds not loading**
   - Federal Reserve RSS feeds may be temporarily down
   - Use `--market-only` to skip policy analysis

3. **Slow performance**
   - Use `--quick` flag for faster analysis
   - Reduce `max_workers` in config.py if experiencing rate limits

### Debug Mode
```bash
python financial_analyzer.py --verbose
```

## 📝 Example Output

```text
🚀 FINANCIAL SENTIMENT ANALYZER WITH POLICY INTEGRATION
======================================================================

📊 MARKET SENTIMENT ANALYSIS
  Overall Sentiment: Positive (+0.145)
  Positive: 65% | Negative: 20% | Neutral: 15%
  Total Articles: 156

🏛️ GOVERNMENT POLICY ANALYSIS
  Policy Sentiment: Mildly Supportive (+0.056)
  Total Policy Articles: 24
  Policy Categories:
    🟢 Monetary Policy: +0.055 (15 articles)
    🟢 Regulatory: +0.057 (9 articles)

🚀 RECOMMENDATION:
  STRONG BUY (Policy Neutral)
  Market Trend: Bullish (+1.23%)
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ⚠️ Disclaimer

This tool is for informational purposes only and should not be considered as financial advice. Always conduct your own research and consult with financial professionals before making investment decisions.

## 🔗 Related Documentation

- [Government Policy Integration Details](GOVERNMENT_POLICY_INTEGRATION.md)
- [API Documentation](docs/api.md) (if available)
- [Configuration Guide](docs/configuration.md) (if available)