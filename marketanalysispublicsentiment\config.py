"""
Configuration module for Financial Sentiment Analyzer

Contains all constants, ticker lists, RSS feeds, and configuration settings.
"""

# Major stock tickers organized by sector
MAJOR_TICKERS = [
    # Tech giants
    'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX', 'ADBE', 'CRM', 'ORCL', 'INTC', 'AMD',
    # Financial
    'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'BRK-B', 'V', 'MA', 'AXP', 'COF', 'USB', 'PNC', 'TFC',
    # Healthcare
    'JNJ', 'UNH', 'PFE', 'ABT', 'TMO', 'DHR', 'BMY', 'AMGN', 'GILD', 'BIIB', 'REGN', 'VRTX',
    # Consumer
    'WMT', 'HD', 'PG', 'KO', 'PEP', 'MCD', 'NKE', 'SBUX', 'TGT', 'COST', 'LOW', 'DIS',
    # Industrial
    'BA', 'CAT', 'GE', 'MMM', 'HON', 'UPS', 'FDX', 'LMT', 'RTX', 'NOC', 'GD',
    # Energy
    'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'MPC', 'VLO', 'PSX', 'KMI', 'OKE',
    # ETFs and Indices
    'SPY', 'QQQ', 'IWM', 'DIA', 'VTI', 'VOO', 'VEA', 'VWO', 'GLD', 'SLV'
]

# Market indices for tracking overall market performance
MARKET_INDICES = {
    'SPY': 'S&P 500',
    'QQQ': 'NASDAQ',
    'DIA': 'Dow Jones',
    'IWM': 'Russell 2000',
    'VTI': 'Total Stock Market'
}

# Government RSS Feeds for Policy News
GOVERNMENT_RSS_FEEDS = {
    'fed_press': {
        'url': 'https://www.federalreserve.gov/feeds/press_all.xml',
        'name': 'Federal Reserve Press Releases',
        'category': 'monetary_policy',
        'impact_weight': 0.9  # High impact on markets
    },
    'fed_monetary': {
        'url': 'https://www.federalreserve.gov/feeds/press_monetary.xml',
        'name': 'Fed Monetary Policy',
        'category': 'monetary_policy',
        'impact_weight': 1.0  # Highest impact
    },
    'fed_speeches': {
        'url': 'https://www.federalreserve.gov/feeds/speeches.xml',
        'name': 'Fed Speeches & Testimony',
        'category': 'monetary_policy',
        'impact_weight': 0.7
    },
    'fed_banking_reg': {
        'url': 'https://www.federalreserve.gov/feeds/press_bcreg.xml',
        'name': 'Fed Banking Regulation',
        'category': 'regulatory',
        'impact_weight': 0.6
    },
    'fed_enforcement': {
        'url': 'https://www.federalreserve.gov/feeds/press_enforcement.xml',
        'name': 'Fed Enforcement Actions',
        'category': 'regulatory',
        'impact_weight': 0.5
    }
}

# Policy keywords that indicate market impact
POLICY_KEYWORDS = {
    'high_impact': [
        'interest rate', 'federal funds rate', 'monetary policy', 'quantitative easing',
        'inflation target', 'recession', 'economic outlook', 'gdp growth',
        'unemployment rate', 'fomc', 'rate hike', 'rate cut', 'dovish', 'hawkish'
    ],
    'medium_impact': [
        'banking regulation', 'stress test', 'capital requirements', 'liquidity',
        'financial stability', 'systemic risk', 'basel', 'dodd-frank',
        'consumer protection', 'enforcement action'
    ],
    'sector_specific': [
        'energy policy', 'healthcare reform', 'tax policy', 'trade policy',
        'infrastructure', 'climate policy', 'technology regulation',
        'antitrust', 'merger', 'acquisition'
    ]
}

# Sector mapping for tickers
SECTOR_MAPPING = {
    # Technology
    'AAPL': 'Technology', 'MSFT': 'Technology', 'GOOGL': 'Technology', 'AMZN': 'Technology',
    'META': 'Technology', 'NVDA': 'Technology', 'NFLX': 'Technology', 'ADBE': 'Technology',
    'CRM': 'Technology', 'ORCL': 'Technology', 'INTC': 'Technology', 'AMD': 'Technology',
    'TSLA': 'Technology',
    # Financial Services
    'JPM': 'Financial', 'BAC': 'Financial', 'WFC': 'Financial', 'GS': 'Financial',
    'MS': 'Financial', 'C': 'Financial', 'BRK-B': 'Financial', 'V': 'Financial',
    'MA': 'Financial', 'AXP': 'Financial', 'COF': 'Financial', 'USB': 'Financial',
    'PNC': 'Financial', 'TFC': 'Financial',
    # Healthcare & Pharmaceuticals
    'JNJ': 'Healthcare', 'UNH': 'Healthcare', 'PFE': 'Healthcare', 'ABT': 'Healthcare',
    'TMO': 'Healthcare', 'DHR': 'Healthcare', 'BMY': 'Healthcare', 'AMGN': 'Healthcare',
    'GILD': 'Healthcare', 'BIIB': 'Healthcare', 'REGN': 'Healthcare', 'VRTX': 'Healthcare',
    # Consumer Goods & Retail
    'WMT': 'Consumer', 'HD': 'Consumer', 'PG': 'Consumer', 'KO': 'Consumer',
    'PEP': 'Consumer', 'MCD': 'Consumer', 'NKE': 'Consumer', 'SBUX': 'Consumer',
    'TGT': 'Consumer', 'COST': 'Consumer', 'LOW': 'Consumer', 'DIS': 'Consumer',
    # Industrial
    'BA': 'Industrial', 'CAT': 'Industrial', 'GE': 'Industrial', 'MMM': 'Industrial',
    'HON': 'Industrial', 'UPS': 'Industrial', 'FDX': 'Industrial', 'LMT': 'Industrial',
    'RTX': 'Industrial', 'NOC': 'Industrial', 'GD': 'Industrial',
    # Energy
    'XOM': 'Energy', 'CVX': 'Energy', 'COP': 'Energy', 'EOG': 'Energy',
    'SLB': 'Energy', 'MPC': 'Energy', 'VLO': 'Energy', 'PSX': 'Energy',
    'KMI': 'Energy', 'OKE': 'Energy',
    # ETFs & Index Funds
    'SPY': 'ETF', 'QQQ': 'ETF', 'IWM': 'ETF', 'DIA': 'ETF',
    'VTI': 'ETF', 'VOO': 'ETF', 'VEA': 'ETF', 'VWO': 'ETF',
    'GLD': 'Commodities', 'SLV': 'Commodities'
}

# Analysis configuration
ANALYSIS_CONFIG = {
    'max_workers': 10,  # Maximum parallel workers for news fetching
    'articles_per_ticker': 3,  # Number of articles to fetch per ticker
    'articles_per_feed': 5,  # Number of articles to fetch per government feed
    'market_data_days': 30,  # Days of market data to fetch
    'price_change_days': 1,  # Days for price change calculation
    'sentiment_thresholds': {
        'positive': 0.1,
        'negative': -0.1,
        'very_positive': 0.2,
        'very_negative': -0.2
    },
    'policy_weight': 0.3,  # Weight of policy sentiment in combined analysis
    'market_weight': 0.7   # Weight of market sentiment in combined analysis
}

# Display configuration
DISPLAY_CONFIG = {
    'top_tickers_count': 5,
    'negative_tickers_count': 3,
    'top_sectors_count': 5,
    'high_impact_articles_count': 3,
    'recent_news_count': 5,
    'separator_length': 70
}
