"""
Data fetching module for Financial Sentiment Analyzer

Handles fetching news from various sources, market data, and government policy feeds.
"""

import yfinance as yf
from datetime import datetime, timedelta
import pytz
import feedparser
from concurrent.futures import ThreadPoolExecutor, as_completed
from config import MAJOR_TICKERS, MARKET_INDICES, GOVERNMENT_RSS_FEEDS, ANALYSIS_CONFIG


def get_time_ago(dt_obj):
    """Calculate how long ago an article was published"""
    # Convert to CDT timezone
    cdt = pytz.timezone('America/Chicago')
    now = datetime.now(cdt)

    # Convert article time to CDT if it has timezone info
    if dt_obj.tzinfo:
        dt_cdt = dt_obj.astimezone(cdt)
    else:
        # Assume UTC if no timezone info
        utc = pytz.UTC
        dt_utc = utc.localize(dt_obj)
        dt_cdt = dt_utc.astimezone(cdt)

    diff = now - dt_cdt

    if diff.days > 0:
        return f"{diff.days} day{'s' if diff.days > 1 else ''} ago"
    elif diff.seconds > 3600:
        hours = diff.seconds // 3600
        return f"{hours} hour{'s' if hours > 1 else ''} ago"
    elif diff.seconds > 60:
        minutes = diff.seconds // 60
        return f"{minutes} minute{'s' if minutes > 1 else ''} ago"
    else:
        return "Just now"


def get_ticker_price_change(ticker, days=1):
    """Get price change for a ticker over specified days"""
    try:
        stock = yf.Ticker(ticker)
        # Get recent data (last 5 days to ensure we have enough data)
        hist = stock.history(period="5d")

        if len(hist) >= 2:
            # Calculate change from days ago to most recent close
            if len(hist) >= days + 1:
                old_price = hist['Close'].iloc[-(days+1)]
                new_price = hist['Close'].iloc[-1]
            else:
                # Fallback to available data
                old_price = hist['Close'].iloc[0]
                new_price = hist['Close'].iloc[-1]

            price_change = ((new_price / old_price) - 1) * 100
            return float(price_change)
        else:
            return 0.0
    except Exception:
        return 0.0


def get_multiple_ticker_prices(tickers, days=1):
    """Get price changes for multiple tickers efficiently"""
    price_changes = {}

    # Use parallel processing for price fetching
    with ThreadPoolExecutor(max_workers=5) as executor:
        future_to_ticker = {executor.submit(get_ticker_price_change, ticker, days): ticker for ticker in tickers}

        for future in as_completed(future_to_ticker):
            ticker = future_to_ticker[future]
            try:
                price_change = future.result()
                price_changes[ticker] = price_change
            except Exception:
                price_changes[ticker] = 0.0

    return price_changes


def get_analyst_recommendation(ticker):
    """Get analyst recommendation from yfinance"""
    try:
        stock = yf.Ticker(ticker)
        info = stock.info

        # Get recommendation data
        recommendation = info.get('recommendationKey', 'N/A')
        recommendation_mean = info.get('recommendationMean', None)
        target_price = info.get('targetMeanPrice', None)
        current_price = info.get('currentPrice', None) or info.get('regularMarketPrice', None)

        # Convert recommendation key to readable format
        rec_mapping = {
            'strong_buy': 'Strong Buy',
            'buy': 'Buy',
            'hold': 'Hold',
            'sell': 'Sell',
            'strong_sell': 'Strong Sell'
        }

        readable_rec = rec_mapping.get(recommendation, recommendation)

        # Calculate upside if we have target and current price
        upside = None
        if target_price and current_price and current_price > 0:
            upside = ((target_price / current_price) - 1) * 100

        return {
            'recommendation': readable_rec,
            'recommendation_mean': recommendation_mean,
            'target_price': target_price,
            'current_price': current_price,
            'upside_potential': upside
        }
    except Exception:
        return {
            'recommendation': 'N/A',
            'recommendation_mean': None,
            'target_price': None,
            'current_price': None,
            'upside_potential': None
        }


def get_multiple_analyst_recommendations(tickers):
    """Get analyst recommendations for multiple tickers efficiently"""
    recommendations = {}

    # Use parallel processing for recommendation fetching
    with ThreadPoolExecutor(max_workers=5) as executor:
        future_to_ticker = {executor.submit(get_analyst_recommendation, ticker): ticker for ticker in tickers}

        for future in as_completed(future_to_ticker):
            ticker = future_to_ticker[future]
            try:
                rec_data = future.result()
                recommendations[ticker] = rec_data
            except Exception:
                recommendations[ticker] = {
                    'recommendation': 'N/A',
                    'recommendation_mean': None,
                    'target_price': None,
                    'current_price': None,
                    'upside_potential': None
                }

    return recommendations


def fetch_news_for_ticker(ticker):
    """Fetch news for a single ticker - optimized for parallel processing"""
    try:
        stock = yf.Ticker(ticker)
        news = stock.news
        
        if not news:
            return []
            
        articles = []
        articles_per_ticker = ANALYSIS_CONFIG['articles_per_ticker']
        
        for article in news[:articles_per_ticker]:  # Top N articles per ticker
            content = article.get('content', article)
            headline = content.get('title', '')
            summary = content.get('summary', '') or content.get('description', '')
            pub_date = content.get('pubDate', '')
            
            if headline and len(headline) > 10:
                # Enhanced date/time parsing with CDT timezone
                try:
                    if pub_date:
                        dt_obj = datetime.fromisoformat(pub_date.replace('Z', '+00:00'))

                        # Convert to CDT
                        cdt = pytz.timezone('America/Chicago')
                        if dt_obj.tzinfo:
                            dt_cdt = dt_obj.astimezone(cdt)
                        else:
                            utc = pytz.UTC
                            dt_utc = utc.localize(dt_obj)
                            dt_cdt = dt_utc.astimezone(cdt)

                        formatted_date = dt_cdt.strftime('%Y-%m-%d')
                        formatted_datetime = dt_cdt.strftime('%Y-%m-%d %H:%M:%S CDT')
                        time_ago = get_time_ago(dt_obj)
                    else:
                        cdt = pytz.timezone('America/Chicago')
                        now = datetime.now(cdt)
                        formatted_date = now.strftime('%Y-%m-%d')
                        formatted_datetime = now.strftime('%Y-%m-%d %H:%M:%S CDT')
                        time_ago = "Just now"
                except:
                    cdt = pytz.timezone('America/Chicago')
                    now = datetime.now(cdt)
                    formatted_date = now.strftime('%Y-%m-%d')
                    formatted_datetime = now.strftime('%Y-%m-%d %H:%M:%S CDT')
                    time_ago = "Unknown"

                articles.append({
                    'headline': headline,
                    'text': summary or headline,
                    'date': formatted_date,
                    'datetime': formatted_datetime,
                    'time_ago': time_ago,
                    'source': f'Yahoo Finance ({ticker})',
                    'ticker': ticker,
                    'url': content.get('canonicalUrl', {}).get('url', '') or content.get('clickThroughUrl', {}).get('url', '')
                })
        
        return articles
    except Exception:
        return []


def fetch_market_news_parallel(quick_mode=False):
    """Fetch news using parallel processing for better performance"""
    tickers = MAJOR_TICKERS[:20] if quick_mode else MAJOR_TICKERS
    
    all_articles = []

    # Parallel processing with ThreadPoolExecutor
    max_workers = ANALYSIS_CONFIG['max_workers']
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_ticker = {executor.submit(fetch_news_for_ticker, ticker): ticker for ticker in tickers}

        for future in as_completed(future_to_ticker):
            articles = future.result()
            all_articles.extend(articles)

    # Remove duplicates efficiently
    unique_articles = []
    seen_headlines = set()

    for article in all_articles:
        headline_key = article['headline'].lower().strip()
        if headline_key not in seen_headlines:
            seen_headlines.add(headline_key)
            unique_articles.append(article)

    # Return articles and stats for dashboard integration
    stats = {
        'total_articles': len(unique_articles),
        'tickers_processed': len(tickers),
        'tickers_with_news': len(set(article['ticker'] for article in unique_articles))
    }

    return (unique_articles if unique_articles else get_sample_data()), stats


def get_sample_data():
    """Fallback sample data if no news is available"""
    print("⚠️ No news from yfinance. Using sample data...")
    current_date = datetime.now().strftime('%Y-%m-%d')
    return [
        {'headline': 'Apple Inc. reports strong quarterly earnings', 'text': 'Apple beats expectations', 'date': current_date, 'source': 'Sample', 'ticker': 'AAPL'},
        {'headline': 'Microsoft announces AI initiatives', 'text': 'Microsoft expands AI', 'date': current_date, 'source': 'Sample', 'ticker': 'MSFT'},
        {'headline': 'Tesla delivery numbers exceed forecasts', 'text': 'Tesla strong deliveries', 'date': current_date, 'source': 'Sample', 'ticker': 'TSLA'},
        {'headline': 'Amazon Web Services growth continues', 'text': 'AWS shows growth', 'date': current_date, 'source': 'Sample', 'ticker': 'AMZN'},
        {'headline': 'NVIDIA benefits from AI demand', 'text': 'NVIDIA AI surge', 'date': current_date, 'source': 'Sample', 'ticker': 'NVDA'}
    ]


def get_market_data_optimized(days=None):
    """Optimized market data fetching - only store what we need"""
    if days is None:
        days = ANALYSIS_CONFIG['market_data_days']

    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)

    market_data = {}

    for ticker, name in MARKET_INDICES.items():
        try:
            data = yf.download(ticker, start=start_date, end=end_date, progress=False, auto_adjust=True)
            if not data.empty:
                price_change = (data['Close'].iloc[-1] / data['Close'].iloc[0] - 1) * 100
                price_change = float(price_change.iloc[0]) if hasattr(price_change, 'iloc') else float(price_change)
                market_data[ticker] = {'name': name, 'price_change': price_change}
        except Exception as e:
            print(f"  Error fetching {ticker}: {e}")

    return market_data


def fetch_government_rss_feed(feed_info):
    """Fetch news from a single government RSS feed"""
    try:
        feed_url = feed_info['url']
        feed_name = feed_info['name']
        category = feed_info['category']
        impact_weight = feed_info['impact_weight']

        # Parse RSS feed
        feed = feedparser.parse(feed_url)

        if not feed.entries:
            return []

        articles = []
        cdt = pytz.timezone('America/Chicago')
        articles_per_feed = ANALYSIS_CONFIG['articles_per_feed']

        for entry in feed.entries[:articles_per_feed]:  # Top N articles per feed
            try:
                headline = entry.get('title', '').strip()
                summary = entry.get('summary', '') or entry.get('description', '')
                link = entry.get('link', '')

                # Parse publication date
                pub_date = entry.get('published_parsed') or entry.get('updated_parsed')
                if pub_date:
                    # Convert from time.struct_time to datetime
                    dt_obj = datetime(*pub_date[:6])
                    # Assume UTC if no timezone info
                    utc = pytz.UTC
                    dt_utc = utc.localize(dt_obj)
                    dt_cdt = dt_utc.astimezone(cdt)

                    formatted_date = dt_cdt.strftime('%Y-%m-%d')
                    formatted_datetime = dt_cdt.strftime('%Y-%m-%d %H:%M:%S CDT')
                    time_ago = get_time_ago(dt_utc)
                else:
                    now = datetime.now(cdt)
                    formatted_date = now.strftime('%Y-%m-%d')
                    formatted_datetime = now.strftime('%Y-%m-%d %H:%M:%S CDT')
                    time_ago = "Unknown"

                if headline and len(headline) > 10:
                    articles.append({
                        'headline': headline,
                        'text': summary or headline,
                        'date': formatted_date,
                        'datetime': formatted_datetime,
                        'time_ago': time_ago,
                        'source': feed_name,
                        'url': link,
                        'category': category,
                        'impact_weight': impact_weight,
                        'ticker': 'POLICY'  # Special ticker for policy news
                    })

            except Exception:
                continue  # Skip problematic entries

        return articles

    except Exception as e:
        print(f"  Error fetching {feed_info['name']}: {e}")
        return []


def fetch_government_news_parallel():
    """Fetch government policy news using parallel processing"""
    all_articles = []

    # Parallel processing with ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=5) as executor:
        future_to_feed = {executor.submit(fetch_government_rss_feed, feed_info): feed_name
                         for feed_name, feed_info in GOVERNMENT_RSS_FEEDS.items()}

        for future in as_completed(future_to_feed):
            articles = future.result()
            all_articles.extend(articles)

    # Remove duplicates
    unique_articles = []
    seen_headlines = set()

    for article in all_articles:
        headline_key = article['headline'].lower().strip()
        if headline_key not in seen_headlines:
            seen_headlines.add(headline_key)
            unique_articles.append(article)

    # Return articles and stats for dashboard integration
    stats = {
        'total_articles': len(unique_articles),
        'sources_processed': len(GOVERNMENT_RSS_FEEDS),
        'sources_with_news': len(set(article['source'] for article in unique_articles))
    }

    return unique_articles, stats
