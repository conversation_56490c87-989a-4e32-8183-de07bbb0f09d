"""
Display utilities module for Financial Sentiment Analyzer

Handles all output formatting and display functions.
"""

from config import DISPLAY_CONFIG
from sentiment_analyzer import get_ticker_sector
from policy_analyzer import get_policy_impact_summary, analyze_policy_categories
import shutil


def get_terminal_width():
    """Get terminal width, default to 120 if unable to determine"""
    try:
        return shutil.get_terminal_size().columns
    except Exception:
        return 120


def create_hyperlink(url, text):
    """Create a clickable hyperlink for terminal output"""
    if url and url.strip():
        # ANSI escape sequence for hyperlinks: \033]8;;URL\033\\TEXT\033]8;;\033\\
        return f"\033]8;;{url}\033\\{text}\033]8;;\033\\"
    else:
        return text


def create_box(title, content, width=42):
    """Create a bordered box for dashboard display using ASCII characters"""
    lines = []

    # Top border
    lines.append("+" + "-" * (width - 2) + "+")

    # Title line - center the title
    title_padded = f" {title} "
    if len(title_padded) > width - 2:
        title_padded = title_padded[:width-5] + "..."
    title_padding = (width - 2 - len(title_padded)) // 2
    title_line = "|" + " " * title_padding + title_padded + " " * (width - 2 - title_padding - len(title_padded)) + "|"
    lines.append(title_line)

    # Separator
    lines.append("+" + "-" * (width - 2) + "+")

    # Content lines
    for line in content:
        line_text = f" {line}"
        if len(line_text) > width - 3:
            line_text = line_text[:width-6] + "..."
        content_line = "|" + f"{line_text:<{width-3}}" + "|"
        lines.append(content_line)

    # Bottom border
    lines.append("+" + "-" * (width - 2) + "+")

    return lines


def print_dashboard_header():
    """Print the main dashboard header"""
    title = "🚀 FINANCIAL SENTIMENT ANALYZER DASHBOARD"
    header_width = 100  # Fixed width for consistency

    # Ensure title fits
    if len(title) > header_width - 4:
        title = "🚀 FINANCIAL ANALYZER DASHBOARD"

    padding = max(0, (header_width - len(title)) // 2)
    header_line = "=" * header_width

    print("\n" + header_line)
    print(" " * padding + title)
    print(header_line)


def print_side_by_side(left_content, right_content, gap=10):
    """Print two content blocks side by side with proper alignment"""
    # Ensure both contents are lists of strings
    if isinstance(left_content, str):
        left_lines = left_content.split('\n')
    else:
        left_lines = left_content[:]

    if isinstance(right_content, str):
        right_lines = right_content.split('\n')
    else:
        right_lines = right_content[:]

    # For boxes, all lines should be the same width (42 chars)
    # For other content, calculate actual width
    if left_lines and left_lines[0].startswith('+'):
        left_width = 42  # Box width
    else:
        left_width = max(len(line) for line in left_lines) if left_lines else 0

    # Pad to same length
    max_lines = max(len(left_lines), len(right_lines))
    while len(left_lines) < max_lines:
        if left_lines and left_lines[0].startswith('+'):
            left_lines.append(' ' * left_width)  # Empty line for boxes
        else:
            left_lines.append('')
    while len(right_lines) < max_lines:
        right_lines.append('')

    # Print side by side
    for left_line, right_line in zip(left_lines, right_lines):
        # Ensure left line is exactly the expected width
        if len(left_line) < left_width:
            left_line = left_line + ' ' * (left_width - len(left_line))
        elif len(left_line) > left_width:
            left_line = left_line[:left_width]

        print(f"{left_line}{' ' * gap}{right_line}")


def print_header(title, width=70):
    """Print a formatted header"""
    print(f"\n{title}")
    print("=" * width)


def print_section_header(title, width=80):
    """Print a formatted section header"""
    print(f"\n{title}")
    print("=" * width)


def get_mood_emoji(sentiment_score, mood_text):
    """Get appropriate emoji based on sentiment score and mood"""
    if sentiment_score > 0.05 or any(word in mood_text.lower() for word in ['positive', 'supportive', 'bullish']):
        return "😊"
    elif sentiment_score < -0.05 or any(word in mood_text.lower() for word in ['negative', 'bearish', 'cautionary']):
        return "😠"
    else:
        return "😐"


def get_score_indicator(sentiment_score):
    """Get color indicator for sentiment score"""
    if sentiment_score > 0.1:
        return "🟢"  # Green for positive
    elif sentiment_score < -0.1:
        return "🔴"  # Red for negative
    else:
        return "🟡"  # Yellow for neutral


def display_dashboard_overview_single_column(sentiment_analysis, policy_analysis, market_health, market_data):
    """Display main dashboard overview in single column format to avoid hyperlink interference"""
    print_dashboard_header()

    # Create a wide single box with all key metrics
    overview_content = []

    # Market sentiment section
    market_mood = sentiment_analysis.get('market_mood', 'N/A')
    market_score = sentiment_analysis.get('average_sentiment', 0)
    market_emoji = get_mood_emoji(market_score, market_mood)
    market_score_indicator = get_score_indicator(market_score)

    overview_content.append(f"📊 MARKET: {market_emoji} {market_mood} | {market_score_indicator} {market_score:+.3f} | 📈 {sentiment_analysis.get('positive_percentage', 0):.0f}% Pos | 📉 {sentiment_analysis.get('negative_percentage', 0):.0f}% Neg | 📊 {sentiment_analysis.get('total_articles', 0)} Articles")

    # Policy sentiment section
    if policy_analysis:
        policy_mood = policy_analysis.get('policy_mood', 'N/A')
        policy_score = policy_analysis.get('policy_sentiment', 0)
        policy_emoji = get_mood_emoji(policy_score, policy_mood)
        policy_score_indicator = get_score_indicator(policy_score)

        overview_content.append(f"🏛️ POLICY: {policy_emoji} {policy_mood} | {policy_score_indicator} {policy_score:+.3f} | 📄 {policy_analysis.get('total_policy_articles', 0)} Articles | ⚡ {len(policy_analysis.get('high_impact_articles', []))} High Impact")
    else:
        overview_content.append("🏛️ POLICY: No policy data available")

    # Recommendation section
    if market_health:
        overview_content.append(f"🚀 RECOMMENDATION: {market_health.get('recommendation', 'N/A')} | {market_health.get('market_trend', 'N/A')} | {market_health.get('average_market_change', 0):+.2f}% Change | Combined: {market_health.get('combined_sentiment', 0):+.3f}")
    else:
        overview_content.append("🚀 RECOMMENDATION: No recommendation available")

    # Market indices section
    if market_data:
        indices_line = "📈 INDICES: "
        indices_parts = []
        for ticker, data in list(market_data.items())[:4]:  # Top 4 indices
            emoji = "📈" if data['price_change'] > 0 else "📉"
            indices_parts.append(f"{emoji} {ticker}: {data['price_change']:+.2f}%")
        indices_line += " | ".join(indices_parts)
        overview_content.append(indices_line)
    else:
        overview_content.append("📈 INDICES: No market data available")

    # Create single wide box
    overview_box = create_box("🚀 FINANCIAL SENTIMENT ANALYZER OVERVIEW", overview_content, 100)

    # Print single box
    for line in overview_box:
        print(line)


def display_dashboard_overview(sentiment_analysis, policy_analysis, market_health, market_data):
    """Display main dashboard overview with key metrics in a compact format"""
    print_dashboard_header()

    # Create market sentiment box
    market_mood = sentiment_analysis.get('market_mood', 'N/A')
    market_score = sentiment_analysis.get('average_sentiment', 0)
    market_emoji = get_mood_emoji(market_score, market_mood)
    market_score_indicator = get_score_indicator(market_score)

    market_content = [
        f"{market_emoji} Mood: {market_mood}",
        f"{market_score_indicator} Score: {market_score:+.3f}",
        f"📈 Pos: {sentiment_analysis.get('positive_percentage', 0):.0f}%",
        f"📉 Neg: {sentiment_analysis.get('negative_percentage', 0):.0f}%",
        f"📊 Articles: {sentiment_analysis.get('total_articles', 0)}"
    ]
    market_box = create_box("📊 MARKET SENTIMENT", market_content)

    # Create policy sentiment box
    if policy_analysis:
        policy_mood = policy_analysis.get('policy_mood', 'N/A')
        policy_score = policy_analysis.get('policy_sentiment', 0)
        policy_emoji = get_mood_emoji(policy_score, policy_mood)
        policy_score_indicator = get_score_indicator(policy_score)

        policy_content = [
            f"{policy_emoji} Mood: {policy_mood}",
            f"{policy_score_indicator} Score: {policy_score:+.3f}",
            f"📄 Articles: {policy_analysis.get('total_policy_articles', 0)}",
            f"⚡ High Impact: {len(policy_analysis.get('high_impact_articles', []))}"
        ]
    else:
        policy_content = ["No policy data available"]
    policy_box = create_box("🏛️ POLICY SENTIMENT", policy_content)

    # Create recommendation box
    if market_health:
        rec_content = [
            f"Action: {market_health.get('recommendation', 'N/A')}",
            f"Trend: {market_health.get('market_trend', 'N/A')}",
            f"Change: {market_health.get('average_market_change', 0):+.2f}%",
            f"Combined: {market_health.get('combined_sentiment', 0):+.3f}"
        ]
    else:
        rec_content = ["No recommendation available"]
    rec_box = create_box("🚀 RECOMMENDATION", rec_content)

    # Create market indices box
    if market_data:
        indices_content = []
        for ticker, data in list(market_data.items())[:4]:  # Top 4 indices
            emoji = "📈" if data['price_change'] > 0 else "📉"
            indices_content.append(f"{emoji} {ticker}: {data['price_change']:+.2f}%")
    else:
        indices_content = ["No market data available"]
    indices_box = create_box("📈 MARKET INDICES", indices_content)

    # Print boxes in 2x2 grid
    print_side_by_side(market_box, policy_box)
    print()
    print_side_by_side(rec_box, indices_box)


def display_market_sentiment(sentiment_analysis):
    """Display market sentiment analysis results"""
    print_header("📊 MARKET SENTIMENT ANALYSIS")
    print(f"  Overall Sentiment: {sentiment_analysis['market_mood']} ({sentiment_analysis['average_sentiment']:+.3f})")
    print(f"  Positive: {sentiment_analysis['positive_percentage']:.0f}% | "
          f"Negative: {sentiment_analysis['negative_percentage']:.0f}% | "
          f"Neutral: {sentiment_analysis['neutral_percentage']:.0f}%")
    print(f"  Total Articles: {sentiment_analysis['total_articles']}")


def display_policy_analysis(policy_analysis):
    """Display government policy analysis results"""
    print_header("🏛️ GOVERNMENT POLICY ANALYSIS")
    print(f"  Policy Sentiment: {policy_analysis['policy_mood']} ({policy_analysis['policy_sentiment']:+.3f})")
    print(f"  Total Policy Articles: {policy_analysis['total_policy_articles']}")

    if policy_analysis['policy_categories']:
        print("  Policy Categories:")
        category_analysis = analyze_policy_categories(policy_analysis)
        for category, data in category_analysis.items():
            print(f"    {data['emoji']} {data['display_name']}: {data['sentiment']:+.3f} ({data['article_count']} articles)")


def display_high_impact_policy_news(policy_analysis):
    """Display high impact policy news"""
    if not policy_analysis['high_impact_articles']:
        return
        
    print_section_header("⚡ HIGH IMPACT POLICY NEWS")
    
    count = DISPLAY_CONFIG['high_impact_articles_count']
    for i, article in enumerate(policy_analysis['high_impact_articles'][:count], 1):
        impact_emoji = "🔥" if article['impact_level'] == 'High' else "⚠️"
        headline_link = create_hyperlink(article.get('url', ''), article['headline'])
        
        print(f"\n  {i}. {impact_emoji} {article['impact_level']} Impact - Score: {article['impact_score']:.2f}")
        print(f"     Sentiment: {article['polarity']:+.3f} | Weighted: {article['weighted_polarity']:+.3f}")
        print(f"     Source: {article.get('source', 'Unknown')}")
        print(f"     [{article.get('time_ago', 'Unknown time')}]: \"{headline_link}\"")
        
        if i < len(policy_analysis['high_impact_articles'][:count]):
            print("     " + "-" * DISPLAY_CONFIG['separator_length'])


def display_dashboard_sectors_and_tickers(sector_rankings, ticker_rankings, price_changes):
    """Display sectors and top tickers in a compact side-by-side format"""
    print("\n" + "-" * 100)

    # Create sectors content
    sectors_content = ["🏭 TOP SECTORS"]
    sectors_content.append("─" * 30)

    for i, sector in enumerate(sector_rankings[:5], 1):
        emoji = "🟢" if sector['average_sentiment'] > 0.1 else "🟡" if sector['average_sentiment'] > 0 else "🔴"
        top_ticker = sector['top_ticker']
        price_change = price_changes.get(top_ticker, 0.0)
        price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"

        sectors_content.append(f"{i}. {emoji} {sector['sector'][:12]}")
        sectors_content.append(f"   Score: {sector['sector_strength']:.2f}")
        sectors_content.append(f"   Top: {top_ticker} {price_emoji}{price_change:+.1f}%")
        if i < 5:
            sectors_content.append("")

    # Create tickers content
    tickers_content = ["🏆 TOP TICKERS"]
    tickers_content.append("─" * 30)

    for i, ticker in enumerate(ticker_rankings[:5], 1):
        ticker_symbol = ticker['ticker']
        price_change = price_changes.get(ticker_symbol, 0.0)
        price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"

        tickers_content.append(f"{i}. {ticker_symbol} ({get_ticker_sector(ticker_symbol)[:8]})")
        tickers_content.append(f"   Score: {ticker['overall_score']:.3f}")
        tickers_content.append(f"   Price: {price_emoji}{price_change:+.1f}%")
        if i < 5:
            tickers_content.append("")

    # Print side by side
    print_side_by_side(sectors_content, tickers_content)


def display_dashboard_news_summary_single_column(news_data, sentiment_scores, sentiment_details, policy_analysis):
    """Display recent news and policy highlights in single column format to avoid hyperlink interference"""
    print("\n" + "=" * 100)
    print("📰 RECENT NEWS & POLICY HIGHLIGHTS")
    print("=" * 100)

    # Market News Section
    print("\n🕒 RECENT MARKET NEWS:")
    print("-" * 50)

    # Combine and sort by recency
    combined_data = []
    for i, article in enumerate(news_data[:20]):  # Limit to recent articles
        if i < len(sentiment_scores):
            combined_data.append({
                'article': article,
                'sentiment_score': sentiment_scores[i]
            })

    combined_data.sort(key=lambda x: x['article'].get('datetime', ''), reverse=True)

    for i, item in enumerate(combined_data[:5], 1):
        article = item['article']
        sentiment_score = item['sentiment_score']

        # Sentiment emoji
        if sentiment_score > 0.1:
            emoji = "🟢"
        elif sentiment_score > -0.1:
            emoji = "🟡"
        else:
            emoji = "🔴"

        ticker = article.get('ticker', 'N/A')
        time_info = article.get('time_ago', 'Unknown')
        headline = article['headline'][:80] + "..." if len(article['headline']) > 80 else article['headline']

        # Create clickable headline
        headline_link = create_hyperlink(article.get('url', ''), headline)

        print(f"{i}. {emoji} {ticker} [{time_info}]: {headline_link}")

    # Policy Highlights Section
    print("\n🏛️ POLICY HIGHLIGHTS:")
    print("-" * 50)

    if policy_analysis and policy_analysis.get('high_impact_articles'):
        for i, article in enumerate(policy_analysis['high_impact_articles'][:5], 1):
            impact_emoji = "🔥" if article['impact_level'] == 'High' else "⚠️"
            headline = article['headline'][:80] + "..." if len(article['headline']) > 80 else article['headline']
            time_info = article.get('time_ago', 'Unknown')

            # Create clickable headline
            headline_link = create_hyperlink(article.get('url', ''), headline)

            print(f"{i}. {impact_emoji} {article['impact_level']} (Score: {article['impact_score']:.1f}) [{time_info}]: {headline_link}")
    else:
        print("No high-impact policy news available")


def display_dashboard_news_summary(news_data, sentiment_scores, sentiment_details, policy_analysis):
    """Display recent news and policy highlights in compact format"""
    print("\n" + "-" * 100)

    # Recent market news (left side)
    news_content = ["🕒 RECENT MARKET NEWS"]
    news_content.append("─" * 35)

    # Combine and sort by recency
    combined_data = []
    for i, article in enumerate(news_data[:20]):  # Limit to recent articles
        if i < len(sentiment_scores):
            combined_data.append({
                'article': article,
                'sentiment_score': sentiment_scores[i]
            })

    combined_data.sort(key=lambda x: x['article'].get('datetime', ''), reverse=True)

    for i, item in enumerate(combined_data[:6], 1):
        article = item['article']
        sentiment_score = item['sentiment_score']

        # Sentiment emoji
        if sentiment_score > 0.1:
            emoji = "🟢"
        elif sentiment_score > -0.1:
            emoji = "🟡"
        else:
            emoji = "🔴"

        ticker = article.get('ticker', 'N/A')
        time_info = article.get('time_ago', 'Unknown')
        headline = article['headline'][:40] + "..." if len(article['headline']) > 40 else article['headline']

        # Create clickable headline
        headline_link = create_hyperlink(article.get('url', ''), headline)

        news_content.append(f"{i}. {emoji} {ticker} [{time_info}]")
        news_content.append(f"   {headline_link}")
        if i < 6:
            news_content.append("")

    # Policy highlights (right side)
    policy_content = ["🏛️ POLICY HIGHLIGHTS"]
    policy_content.append("─" * 35)

    if policy_analysis and policy_analysis.get('high_impact_articles'):
        for i, article in enumerate(policy_analysis['high_impact_articles'][:6], 1):
            impact_emoji = "🔥" if article['impact_level'] == 'High' else "⚠️"
            headline = article['headline'][:40] + "..." if len(article['headline']) > 40 else article['headline']
            time_info = article.get('time_ago', 'Unknown')

            # Create clickable headline
            headline_link = create_hyperlink(article.get('url', ''), headline)

            policy_content.append(f"{i}. {impact_emoji} {article['impact_level']}")
            policy_content.append(f"   Score: {article['impact_score']:.1f} [{time_info}]")
            policy_content.append(f"   {headline_link}")
            if i < 6:
                policy_content.append("")
    else:
        policy_content.append("No high-impact policy news")

    # Print side by side
    print_side_by_side(news_content, policy_content)


def display_sector_performance(sector_rankings, price_changes):
    """Display top sector performance"""
    print_header("🏭 TOP SECTOR PERFORMANCE")

    count = DISPLAY_CONFIG['top_sectors_count']
    for i, sector in enumerate(sector_rankings[:count], 1):
        emoji = "🟢" if sector['average_sentiment'] > 0.1 else "🟡" if sector['average_sentiment'] > 0 else "🔴"
        top_ticker = sector['top_ticker']
        price_change = price_changes.get(top_ticker, 0.0)
        price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"

        print(f"  {i}. {emoji} {sector['sector']} - Strength: {sector['sector_strength']:.3f}")
        print(f"     Avg Sentiment: {sector['average_sentiment']:+.3f} | "
              f"Tickers: {sector['ticker_count']} | "
              f"Positive: {sector['positive_percentage']:.0f}%")
        print(f"     Top Performer: {top_ticker} (Score: {sector['top_ticker_score']:.3f}) "
              f"{price_emoji} {price_change:+.2f}%")


def display_top_tickers(ticker_rankings, price_changes, recommendations):
    """Display top sentiment tickers"""
    print_section_header("🏆 TOP 5 BEST SENTIMENT TICKERS")

    count = DISPLAY_CONFIG['top_tickers_count']
    for i, ticker in enumerate(ticker_rankings[:count], 1):
        sector = get_ticker_sector(ticker['ticker'])
        ticker_symbol = ticker['ticker']
        price_change = price_changes.get(ticker_symbol, 0.0)
        price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"

        # Get analyst recommendation
        rec_data = recommendations.get(ticker_symbol, {})
        analyst_rec = rec_data.get('recommendation', 'N/A')
        upside = rec_data.get('upside_potential', None)

        # Create recommendation display
        rec_display = f"Analyst: {analyst_rec}"
        if upside is not None:
            upside_emoji = "🎯" if upside > 10 else "📊" if upside > 0 else "⚠️"
            rec_display += f" {upside_emoji} {upside:+.1f}% upside"

        # Create clickable headline
        headline_link = create_hyperlink(ticker['best_headline_url'], ticker['best_headline'])

        print(f"\n  {i}. {ticker_symbol} ({sector}) - Score: {ticker['overall_score']:.3f} {price_emoji} {price_change:+.2f}%")
        print(f"     Sentiment: {ticker['average_sentiment']:+.3f} | "
              f"Articles: {ticker['total_articles']} | "
              f"Positive: {ticker['positive_percentage']:.0f}%")
        print(f"     {rec_display}")
        print(f"     Best News [{ticker['best_headline_time']}]: \"{headline_link}\"")
        print(f"     Published: {ticker['best_headline_datetime']}")

        # Add separator line between tickers (except for the last one)
        if i < count:
            print("     " + "-" * DISPLAY_CONFIG['separator_length'])


def display_negative_tickers(ticker_rankings, price_changes, recommendations):
    """Display tickers with negative sentiment"""
    negative_tickers = [t for t in ticker_rankings if t['average_sentiment'] < -0.05]
    if not negative_tickers:
        print("\n✅ No tickers with significantly negative sentiment found!")
        return

    print_section_header("⚠️ TICKERS TO WATCH (Negative Sentiment)")

    count = DISPLAY_CONFIG['negative_tickers_count']
    for i, ticker in enumerate(negative_tickers[:count], 1):
        sector = get_ticker_sector(ticker['ticker'])
        ticker_symbol = ticker['ticker']
        price_change = price_changes.get(ticker_symbol, 0.0)
        price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"

        # Get analyst recommendation
        rec_data = recommendations.get(ticker_symbol, {})
        analyst_rec = rec_data.get('recommendation', 'N/A')
        upside = rec_data.get('upside_potential', None)

        # Create recommendation display
        rec_display = f"Analyst: {analyst_rec}"
        if upside is not None:
            upside_emoji = "🎯" if upside > 10 else "📊" if upside > 0 else "⚠️"
            rec_display += f" {upside_emoji} {upside:+.1f}% upside"

        # Create clickable headline
        headline_link = create_hyperlink(ticker['worst_headline_url'], ticker['worst_headline'])

        print(f"\n  {i}. {ticker_symbol} ({sector}) - Score: {ticker['average_sentiment']:+.3f} {price_emoji} {price_change:+.2f}%")
        print(f"     Negative: {ticker['negative_percentage']:.0f}% | Articles: {ticker['total_articles']}")
        print(f"     {rec_display}")
        print(f"     Concerning [{ticker['worst_headline_time']}]: \"{headline_link}\"")
        print(f"     Published: {ticker['worst_headline_datetime']}")

        # Add separator line between tickers (except for the last one)
        if i < len(negative_tickers[:count]):
            print("     " + "-" * DISPLAY_CONFIG['separator_length'])


def display_combined_analysis(market_health):
    """Display combined market and policy analysis"""
    print_header("🎯 COMBINED MARKET & POLICY ANALYSIS")
    
    combined_sentiment = market_health.get('combined_sentiment', 0)
    policy_influence = market_health.get('policy_influence', 0)
    
    print(f"  Combined Score: {combined_sentiment:+.3f}")
    print(f"  Policy Influence: {policy_influence:+.3f}")
    
    # Policy impact assessment
    if abs(policy_influence) > 0.1:
        if policy_influence > 0:
            policy_impact = "🟢 Government policies are providing significant market support"
        else:
            policy_impact = "🔴 Government policies are creating market headwinds"
    elif abs(policy_influence) > 0.05:
        if policy_influence > 0:
            policy_impact = "🟡 Government policies are mildly supportive"
        else:
            policy_impact = "🟡 Government policies are creating mild concerns"
    else:
        policy_impact = "⚪ Government policies have neutral market impact"
    
    print(f"  Policy Assessment: {policy_impact}")


def display_recommendation(market_health):
    """Display trading recommendation"""
    print_header("🚀 RECOMMENDATION")
    print(f"  {market_health['recommendation']}")
    print(f"  Market Trend: {market_health['market_trend']} ({market_health['average_market_change']:+.2f}%)")


def display_market_indices(market_data):
    """Display market indices performance"""
    print_header("📈 MARKET INDICES PERFORMANCE")
    for index_ticker, data in market_data.items():
        emoji = "📈" if data['price_change'] > 0 else "📉"
        print(f"  {emoji} {data['name']} ({index_ticker}): {data['price_change']:+.2f}%")


def display_recent_news_timeline(news_data, limit=None):
    """Display recent news timeline"""
    print_header("🕒 RECENT NEWS TIMELINE")

    recent_articles = sorted(news_data, key=lambda x: x.get('datetime', ''), reverse=True)
    count = limit if limit is not None else DISPLAY_CONFIG['recent_news_count']

    for i, article in enumerate(recent_articles[:count], 1):
        time_info = article.get('time_ago', 'Unknown time')
        ticker = article.get('ticker', 'N/A')
        headline = article['headline'][:80] + "..." if len(article['headline']) > 80 else article['headline']
        headline_link = create_hyperlink(article.get('url', ''), headline)

        print(f"  {i}. [{time_info}] {ticker}: \"{headline_link}\"")
        print(f"     Published: {article.get('datetime', 'Unknown')}")


def display_sentiment_ranked_timeline(news_data, sentiment_scores, sentiment_details, limit=15):
    """Display news timeline chronologically with sentiment scores"""
    print_header("🕒 RECENT NEWS TIMELINE")

    # Combine news data with sentiment scores
    combined_data = []
    for i, article in enumerate(news_data):
        if i < len(sentiment_scores) and i < len(sentiment_details):
            combined_data.append({
                'article': article,
                'sentiment_score': sentiment_scores[i],
                'sentiment_detail': sentiment_details[i]
            })

    # Sort by recency (most recent first)
    combined_data.sort(key=lambda x: x['article'].get('datetime', ''), reverse=True)

    for i, item in enumerate(combined_data[:limit], 1):
        article = item['article']
        sentiment_score = item['sentiment_score']

        time_info = article.get('time_ago', 'Unknown time')
        ticker = article.get('ticker', 'N/A')
        headline = article['headline'][:75] + "..." if len(article['headline']) > 75 else article['headline']
        headline_link = create_hyperlink(article.get('url', ''), headline)

        # Sentiment emoji and color coding
        if sentiment_score > 0.1:
            sentiment_emoji = "🟢"
            sentiment_label = "Positive"
        elif sentiment_score > 0.05:
            sentiment_emoji = "🟡"
            sentiment_label = "Mildly Positive"
        elif sentiment_score > -0.05:
            sentiment_emoji = "⚪"
            sentiment_label = "Neutral"
        elif sentiment_score > -0.1:
            sentiment_emoji = "🟡"
            sentiment_label = "Mildly Negative"
        else:
            sentiment_emoji = "🔴"
            sentiment_label = "Negative"

        print(f"  {i}. {sentiment_emoji} [{time_info}] {ticker}: \"{headline_link}\"")
        print(f"     Sentiment: {sentiment_score:+.3f} ({sentiment_label}) | Published: {article.get('datetime', 'Unknown')}")

        # Add separator line between articles (except for the last one)
        if i < min(len(combined_data), limit):
            print()


def print_help():
    """Print comprehensive help information"""
    help_text = """
🚀 FINANCIAL SENTIMENT ANALYZER WITH POLICY INTEGRATION

DESCRIPTION:
    A comprehensive tool for analyzing market sentiment from news sources and 
    government policy announcements to provide trading insights.

USAGE:
    python financial_analyzer.py [options]

OPTIONS:
    --help, -h              Show this help message
    --market-only           Run only market sentiment analysis
    --policy-only           Run only government policy analysis
    --sectors               Show detailed sector analysis
    --tickers               Show detailed ticker rankings
    --recommendations       Show analyst recommendations
    --indices               Show market indices performance
    --timeline              Show recent news timeline with sentiment scores (10-15 items)
    --dashboard             Show compact dashboard view (default for full analysis)
    --detailed              Show traditional detailed output format
    --quick                 Quick analysis (fewer sources, faster)
    --verbose               Verbose output with debug information

EXAMPLES:
    python financial_analyzer.py
        Run full analysis with compact dashboard view

    python financial_analyzer.py --detailed
        Run full analysis with traditional detailed output

    python financial_analyzer.py --market-only
        Analyze only market sentiment (no government policy)

    python financial_analyzer.py --policy-only
        Analyze only government policy impact

    python financial_analyzer.py --quick
        Quick analysis using fewer data sources

    python financial_analyzer.py --sectors --tickers
        Show detailed sector and ticker analysis

    python financial_analyzer.py --timeline
        Show only recent news timeline with sentiment scores (15 items)

    python financial_analyzer.py --timeline --quick
        Quick news timeline with sentiment scores (faster, fewer sources)

FEATURES:
    • Market sentiment analysis from 80+ major tickers
    • Government policy impact analysis from Federal Reserve feeds
    • Sector performance rankings
    • Individual ticker sentiment scores
    • Analyst recommendations integration
    • Combined market and policy recommendations
    • Real-time news timeline with clickable links

DATA SOURCES:
    • Yahoo Finance (stock news and prices)
    • Federal Reserve RSS feeds (monetary policy)
    • Government regulatory announcements
    • Analyst recommendations and price targets

OUTPUT SECTIONS:
    • Market Sentiment Analysis
    • Government Policy Analysis  
    • High Impact Policy News
    • Top Sector Performance
    • Best/Worst Sentiment Tickers
    • Combined Analysis & Recommendations
    • Market Indices Performance
    • Recent News Timeline

For more information, see README.md
"""
    print(help_text)


def display_full_dashboard_single_column(sentiment_analysis, policy_analysis, market_health, market_data,
                                        sector_rankings, ticker_rankings, price_changes, news_data,
                                        sentiment_scores, sentiment_details):
    """Display the complete dashboard in single column format to avoid hyperlink interference"""

    # Main overview dashboard - single column
    display_dashboard_overview_single_column(sentiment_analysis, policy_analysis, market_health, market_data)

    # Sectors and tickers side by side (this should be fine)
    if sector_rankings and ticker_rankings:
        display_dashboard_sectors_and_tickers(sector_rankings, ticker_rankings, price_changes)

    # News and policy highlights - single column
    if news_data and sentiment_scores:
        display_dashboard_news_summary_single_column(news_data, sentiment_scores, sentiment_details, policy_analysis)

    # Footer with key insights
    footer_line = "=" * 100
    print("\n" + footer_line)
    if market_health:
        combined_sentiment = market_health.get('combined_sentiment', 0)
        policy_influence = market_health.get('policy_influence', 0)

        insights = []
        if abs(combined_sentiment) > 0.1:
            if combined_sentiment > 0:
                insights.append("🟢 Strong positive market sentiment")
            else:
                insights.append("🔴 Strong negative market sentiment")

        if abs(policy_influence) > 0.05:
            if policy_influence > 0:
                insights.append("🏛️ Policy support detected")
            else:
                insights.append("🏛️ Policy headwinds present")

        if not insights:
            insights.append("📊 Market sentiment is neutral")

        print("💡 KEY INSIGHTS: " + " | ".join(insights))

    print(footer_line)


def display_full_dashboard(sentiment_analysis, policy_analysis, market_health, market_data,
                          sector_rankings, ticker_rankings, price_changes, news_data,
                          sentiment_scores, sentiment_details):
    """Display the complete dashboard in a compact format"""

    # Main overview dashboard
    display_dashboard_overview(sentiment_analysis, policy_analysis, market_health, market_data)

    # Sectors and tickers side by side
    if sector_rankings and ticker_rankings:
        display_dashboard_sectors_and_tickers(sector_rankings, ticker_rankings, price_changes)

    # News and policy highlights
    if news_data and sentiment_scores:
        display_dashboard_news_summary(news_data, sentiment_scores, sentiment_details, policy_analysis)

    # Footer with key insights and URL access info
    footer_line = "=" * 100
    print("\n" + footer_line)
    if market_health:
        combined_sentiment = market_health.get('combined_sentiment', 0)
        policy_influence = market_health.get('policy_influence', 0)

        insights = []
        if abs(combined_sentiment) > 0.1:
            if combined_sentiment > 0:
                insights.append("🟢 Strong positive market sentiment")
            else:
                insights.append("🔴 Strong negative market sentiment")

        if abs(policy_influence) > 0.05:
            if policy_influence > 0:
                insights.append("🏛️ Policy support detected")
            else:
                insights.append("🏛️ Policy headwinds present")

        if not insights:
            insights.append("📊 Market sentiment is neutral")

        print("💡 KEY INSIGHTS: " + " | ".join(insights))

    print(footer_line)
