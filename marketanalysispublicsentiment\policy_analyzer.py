"""
Government policy analysis module for Financial Sentiment Analyzer

Handles analysis of government policy news and their market impact.
"""

from textblob import TextBlob
import numpy as np
from config import P<PERSON><PERSON><PERSON>_KEYWORDS


def classify_policy_impact(article):
    """Classify the potential market impact of a policy article"""
    text = f"{article['headline']} {article.get('text', '')}".lower()
    
    # Check for high impact keywords
    high_impact_score = sum(1 for keyword in POLICY_KEYWORDS['high_impact'] if keyword in text)
    medium_impact_score = sum(1 for keyword in POLICY_KEYWORDS['medium_impact'] if keyword in text)
    sector_impact_score = sum(1 for keyword in POLICY_KEYWORDS['sector_specific'] if keyword in text)
    
    # Calculate impact score
    impact_score = (high_impact_score * 3 + medium_impact_score * 2 + sector_impact_score * 1) * article.get('impact_weight', 0.5)
    
    # Classify impact level
    if impact_score >= 3:
        impact_level = 'High'
    elif impact_score >= 1.5:
        impact_level = 'Medium'
    elif impact_score >= 0.5:
        impact_level = 'Low'
    else:
        impact_level = 'Minimal'
    
    return {
        'impact_level': impact_level,
        'impact_score': impact_score,
        'high_impact_keywords': high_impact_score,
        'medium_impact_keywords': medium_impact_score,
        'sector_impact_keywords': sector_impact_score
    }


def analyze_policy_sentiment(government_articles):
    """Analyze sentiment of government policy news with market impact weighting"""
    if not government_articles:
        return {
            'policy_sentiment': 0,
            'policy_mood': 'No Policy Data',
            'high_impact_articles': [],
            'policy_categories': {},
            'total_policy_articles': 0
        }
    
    policy_scores = []
    policy_details = []
    high_impact_articles = []
    category_sentiment = {}
    
    for article in government_articles:
        try:
            # Get basic sentiment
            text = f"{article['headline']} {article.get('text', '')}"
            blob = TextBlob(text)
            base_polarity = blob.sentiment.polarity
            
            # Get policy impact classification
            impact_info = classify_policy_impact(article)
            
            # Weight sentiment by impact score
            weighted_polarity = base_polarity * (1 + impact_info['impact_score'] * 0.5)
            
            policy_scores.append(weighted_polarity)
            
            article_detail = {
                'headline': article['headline'],
                'polarity': base_polarity,
                'weighted_polarity': weighted_polarity,
                'impact_level': impact_info['impact_level'],
                'impact_score': impact_info['impact_score'],
                'category': article.get('category', 'unknown'),
                'source': article.get('source', ''),
                'time_ago': article.get('time_ago', ''),
                'url': article.get('url', '')
            }
            
            policy_details.append(article_detail)
            
            # Track high impact articles
            if impact_info['impact_level'] in ['High', 'Medium']:
                high_impact_articles.append(article_detail)
            
            # Track sentiment by category
            category = article.get('category', 'unknown')
            if category not in category_sentiment:
                category_sentiment[category] = []
            category_sentiment[category].append(weighted_polarity)
            
        except Exception:
            policy_scores.append(0)
            policy_details.append({
                'headline': article.get('headline', ''),
                'polarity': 0,
                'weighted_polarity': 0,
                'impact_level': 'Minimal',
                'impact_score': 0,
                'category': article.get('category', 'unknown')
            })
    
    # Calculate overall policy sentiment
    avg_policy_sentiment = np.mean(policy_scores) if policy_scores else 0
    
    # Determine policy mood
    if avg_policy_sentiment > 0.15:
        policy_mood = "Market Supportive"
    elif avg_policy_sentiment > 0.05:
        policy_mood = "Mildly Supportive"
    elif avg_policy_sentiment > -0.05:
        policy_mood = "Neutral"
    elif avg_policy_sentiment > -0.15:
        policy_mood = "Cautionary"
    else:
        policy_mood = "Market Negative"
    
    # Calculate category averages
    category_averages = {}
    for category, scores in category_sentiment.items():
        category_averages[category] = {
            'average_sentiment': np.mean(scores),
            'article_count': len(scores)
        }
    
    return {
        'policy_sentiment': avg_policy_sentiment,
        'policy_mood': policy_mood,
        'high_impact_articles': sorted(high_impact_articles, key=lambda x: x['impact_score'], reverse=True)[:5],
        'policy_categories': category_averages,
        'total_policy_articles': len(government_articles),
        'policy_details': policy_details
    }


def get_policy_impact_summary(policy_analysis):
    """Generate a summary of policy impact for display"""
    if not policy_analysis or policy_analysis['total_policy_articles'] == 0:
        return "No policy data available"
    
    policy_sentiment = policy_analysis['policy_sentiment']
    high_impact_count = len([a for a in policy_analysis['high_impact_articles'] if a['impact_level'] == 'High'])
    medium_impact_count = len([a for a in policy_analysis['high_impact_articles'] if a['impact_level'] == 'Medium'])
    
    summary_parts = []
    
    # Sentiment assessment
    if policy_sentiment > 0.1:
        summary_parts.append("🟢 Policies are market-supportive")
    elif policy_sentiment > 0.05:
        summary_parts.append("🟡 Policies are mildly supportive")
    elif policy_sentiment > -0.05:
        summary_parts.append("⚪ Policies have neutral impact")
    elif policy_sentiment > -0.1:
        summary_parts.append("🟡 Policies create mild concerns")
    else:
        summary_parts.append("🔴 Policies create market headwinds")
    
    # Impact level assessment
    if high_impact_count > 0:
        summary_parts.append(f"⚡ {high_impact_count} high-impact announcement{'s' if high_impact_count > 1 else ''}")
    if medium_impact_count > 0:
        summary_parts.append(f"⚠️ {medium_impact_count} medium-impact announcement{'s' if medium_impact_count > 1 else ''}")
    
    return " | ".join(summary_parts)


def get_policy_recommendations(policy_analysis, market_sentiment):
    """Generate policy-based trading recommendations"""
    if not policy_analysis or policy_analysis['total_policy_articles'] == 0:
        return []
    
    recommendations = []
    policy_sentiment = policy_analysis['policy_sentiment']
    high_impact_articles = policy_analysis['high_impact_articles']
    
    # High-level policy recommendation
    if policy_sentiment > 0.1 and market_sentiment > 0.05:
        recommendations.append("🚀 Policy and market alignment suggests strong buying opportunity")
    elif policy_sentiment > 0.05 and market_sentiment < -0.05:
        recommendations.append("⚖️ Positive policy sentiment may offset negative market sentiment")
    elif policy_sentiment < -0.1 and market_sentiment > 0.05:
        recommendations.append("⚠️ Policy headwinds may limit market gains")
    elif policy_sentiment < -0.05 and market_sentiment < -0.05:
        recommendations.append("🔴 Policy and market sentiment both negative - exercise caution")
    
    # Specific policy-based recommendations
    for article in high_impact_articles[:2]:  # Top 2 high impact articles
        if article['impact_level'] == 'High':
            if article['polarity'] > 0.1:
                recommendations.append(f"📈 Monitor sectors affected by: {article['headline'][:60]}...")
            elif article['polarity'] < -0.1:
                recommendations.append(f"📉 Consider defensive positioning due to: {article['headline'][:60]}...")
    
    return recommendations


def analyze_policy_categories(policy_analysis):
    """Analyze policy sentiment by category"""
    if not policy_analysis or not policy_analysis['policy_categories']:
        return {}
    
    category_analysis = {}
    
    for category, data in policy_analysis['policy_categories'].items():
        sentiment = data['average_sentiment']
        count = data['article_count']
        
        # Determine category impact
        if sentiment > 0.1:
            impact = "Strongly Positive"
            emoji = "🟢"
        elif sentiment > 0.05:
            impact = "Positive"
            emoji = "🟢"
        elif sentiment > -0.05:
            impact = "Neutral"
            emoji = "🟡"
        elif sentiment > -0.1:
            impact = "Negative"
            emoji = "🔴"
        else:
            impact = "Strongly Negative"
            emoji = "🔴"
        
        category_analysis[category] = {
            'sentiment': sentiment,
            'article_count': count,
            'impact': impact,
            'emoji': emoji,
            'display_name': category.replace('_', ' ').title()
        }
    
    return category_analysis


def get_policy_timeline(policy_analysis, limit=5):
    """Get recent policy news timeline"""
    if not policy_analysis or not policy_analysis['policy_details']:
        return []
    
    # Sort by impact score and recency
    sorted_articles = sorted(
        policy_analysis['policy_details'],
        key=lambda x: (x['impact_score'], x.get('time_ago', 'Unknown')),
        reverse=True
    )
    
    timeline = []
    for article in sorted_articles[:limit]:
        timeline.append({
            'headline': article['headline'],
            'time_ago': article.get('time_ago', 'Unknown'),
            'impact_level': article['impact_level'],
            'sentiment': article['polarity'],
            'source': article.get('source', 'Unknown'),
            'url': article.get('url', '')
        })
    
    return timeline
