"""
Sentiment analysis module for Financial Sentiment Analyzer

Handles sentiment analysis for both market news and government policy news.
"""

from textblob import TextBlob
import numpy as np
from functools import lru_cache
from config import SECTOR_MAPPING, POLICY_KEYWORDS, ANALYSIS_CONFIG


@lru_cache(maxsize=128)
def get_ticker_sector(ticker):
    """Map tickers to their sectors/industries - cached for performance"""
    return SECTOR_MAPPING.get(ticker, 'Other')


def analyze_sentiment_batch(news_data):
    """Optimized sentiment analysis"""
    sentiment_scores = []
    sentiment_details = []
    
    for article in news_data:
        try:
            text = f"{article['headline']} {article.get('text', '')}"
            blob = TextBlob(text)
            polarity = blob.sentiment.polarity
            
            sentiment_scores.append(polarity)
            sentiment_details.append({
                'headline': article['headline'],
                'polarity': polarity,
                'category': "Positive" if polarity > 0.1 else "Negative" if polarity < -0.1 else "Neutral"
            })
        except Exception:
            sentiment_scores.append(0)
            sentiment_details.append({
                'headline': article.get('headline', ''),
                'polarity': 0,
                'category': "Neutral"
            })
    
    return sentiment_scores, sentiment_details


def calculate_market_metrics(sentiment_scores, sentiment_details):
    """Calculate market sentiment metrics efficiently"""
    if not sentiment_scores:
        return {
            "market_mood": "No Data", 
            "average_sentiment": 0, 
            "positive_percentage": 0, 
            "negative_percentage": 0, 
            "neutral_percentage": 0, 
            "total_articles": 0
        }
    
    avg_sentiment = np.mean(sentiment_scores)
    categories = [detail['category'] for detail in sentiment_details]
    total = len(categories)
    
    positive_pct = (categories.count('Positive') / total) * 100
    negative_pct = (categories.count('Negative') / total) * 100
    neutral_pct = (categories.count('Neutral') / total) * 100
    
    # Determine market mood
    thresholds = ANALYSIS_CONFIG['sentiment_thresholds']
    if avg_sentiment > thresholds['very_positive']:
        market_mood = "Very Positive"
    elif avg_sentiment > thresholds['positive']:
        market_mood = "Positive"
    elif avg_sentiment > thresholds['negative']:
        market_mood = "Neutral"
    elif avg_sentiment > thresholds['very_negative']:
        market_mood = "Negative"
    else:
        market_mood = "Very Negative"
    
    return {
        'market_mood': market_mood,
        'average_sentiment': avg_sentiment,
        'positive_percentage': positive_pct,
        'negative_percentage': negative_pct,
        'neutral_percentage': neutral_pct,
        'total_articles': total
    }


def analyze_ticker_sentiment_optimized(news_data, sentiment_details):
    """Optimized ticker sentiment analysis"""
    ticker_sentiment = {}

    # Group articles by ticker
    ticker_articles = {}
    for i, article in enumerate(news_data):
        ticker = article.get('ticker')
        if ticker:
            if ticker not in ticker_articles:
                ticker_articles[ticker] = []
            ticker_articles[ticker].append((article, sentiment_details[i]))

    # Calculate metrics for each ticker
    for ticker, articles_with_sentiment in ticker_articles.items():
        scores = [sentiment['polarity'] for _, sentiment in articles_with_sentiment]
        categories = [sentiment['category'] for _, sentiment in articles_with_sentiment]

        total_articles = len(scores)
        positive_count = categories.count('Positive')
        negative_count = categories.count('Negative')
        neutral_count = categories.count('Neutral')

        avg_sentiment = np.mean(scores)
        sentiment_volatility = np.std(scores) if len(scores) > 1 else 0
        sentiment_consistency = 1 / (1 + sentiment_volatility)
        overall_score = avg_sentiment * sentiment_consistency

        # Find best and worst headlines with timestamps
        best_article = max(articles_with_sentiment, key=lambda x: x[1]['polarity'])[0]
        worst_article = min(articles_with_sentiment, key=lambda x: x[1]['polarity'])[0]

        ticker_sentiment[ticker] = {
            'average_sentiment': avg_sentiment,
            'sentiment_volatility': sentiment_volatility,
            'sentiment_consistency': sentiment_consistency,
            'overall_score': overall_score,
            'total_articles': total_articles,
            'positive_count': positive_count,
            'negative_count': negative_count,
            'neutral_count': neutral_count,
            'positive_percentage': (positive_count / total_articles) * 100,
            'negative_percentage': (negative_count / total_articles) * 100,
            'best_headline': best_article['headline'],
            'best_headline_time': best_article.get('time_ago', 'Unknown time'),
            'best_headline_datetime': best_article.get('datetime', 'Unknown'),
            'best_headline_url': best_article.get('url', ''),
            'worst_headline': worst_article['headline'],
            'worst_headline_time': worst_article.get('time_ago', 'Unknown time'),
            'worst_headline_datetime': worst_article.get('datetime', 'Unknown'),
            'worst_headline_url': worst_article.get('url', '')
        }

    return ticker_sentiment


def analyze_sector_sentiment_optimized(ticker_sentiment):
    """Optimized sector sentiment analysis"""
    sector_sentiment = {}

    # Group tickers by sector
    for ticker, data in ticker_sentiment.items():
        sector = get_ticker_sector(ticker)

        if sector not in sector_sentiment:
            sector_sentiment[sector] = {
                'tickers': [],
                'sentiment_scores': [],
                'total_articles': 0,
                'positive_count': 0,
                'negative_count': 0,
                'neutral_count': 0
            }

        sector_data = sector_sentiment[sector]
        sector_data['tickers'].append({
            'ticker': ticker,
            'overall_score': data['overall_score'],
            'average_sentiment': data['average_sentiment']
        })

        # Aggregate counts
        sector_data['total_articles'] += data['total_articles']
        sector_data['positive_count'] += data['positive_count']
        sector_data['negative_count'] += data['negative_count']
        sector_data['neutral_count'] += data['neutral_count']
        sector_data['sentiment_scores'].append(data['average_sentiment'])

    # Calculate sector metrics
    sector_rankings = []
    for sector, data in sector_sentiment.items():
        if data['sentiment_scores']:
            avg_sentiment = np.mean(data['sentiment_scores'])
            data['tickers'].sort(key=lambda x: x['overall_score'], reverse=True)

            # Sector strength = average of top 3 performers
            top_performers = data['tickers'][:3]
            sector_strength = np.mean([t['overall_score'] for t in top_performers])

            total_articles = data['total_articles']
            positive_pct = (data['positive_count'] / total_articles) * 100 if total_articles > 0 else 0

            sector_rankings.append({
                'sector': sector,
                'average_sentiment': avg_sentiment,
                'sector_strength': sector_strength,
                'ticker_count': len(data['tickers']),
                'total_articles': total_articles,
                'positive_percentage': positive_pct,
                'top_ticker': data['tickers'][0]['ticker'] if data['tickers'] else 'N/A',
                'top_ticker_score': data['tickers'][0]['overall_score'] if data['tickers'] else 0
            })

    return sorted(sector_rankings, key=lambda x: x['sector_strength'], reverse=True)


def rank_tickers_optimized(ticker_sentiment):
    """Optimized ticker ranking - single sort operation"""
    ticker_infos = []

    for ticker, data in ticker_sentiment.items():
        ticker_infos.append({
            'ticker': ticker,
            'average_sentiment': data['average_sentiment'],
            'overall_score': data['overall_score'],
            'total_articles': data['total_articles'],
            'positive_percentage': data['positive_percentage'],
            'negative_percentage': data['negative_percentage'],
            'best_headline': data['best_headline'],
            'best_headline_time': data['best_headline_time'],
            'best_headline_datetime': data['best_headline_datetime'],
            'best_headline_url': data['best_headline_url'],
            'worst_headline': data['worst_headline'],
            'worst_headline_time': data['worst_headline_time'],
            'worst_headline_datetime': data['worst_headline_datetime'],
            'worst_headline_url': data['worst_headline_url']
        })

    # Return only the best overall ranking (most important)
    return sorted(ticker_infos, key=lambda x: x['overall_score'], reverse=True)


def analyze_market_health_optimized(market_data, sentiment_analysis, policy_analysis=None):
    """Optimized market health analysis with policy integration"""
    if not market_data:
        return {"recommendation": "INSUFFICIENT DATA", "market_trend": "Unknown"}

    price_changes = [data['price_change'] for data in market_data.values()]
    avg_market_change = np.mean(price_changes)

    # Determine market trend
    if avg_market_change > 2:
        market_trend = "Strong Bullish"
    elif avg_market_change > 0.5:
        market_trend = "Bullish"
    elif avg_market_change > -0.5:
        market_trend = "Sideways"
    elif avg_market_change > -2:
        market_trend = "Bearish"
    else:
        market_trend = "Strong Bearish"

    # Generate recommendation with policy consideration
    sentiment_score = sentiment_analysis.get('average_sentiment', 0)
    policy_score = policy_analysis.get('policy_sentiment', 0) if policy_analysis else 0
    
    # Combine sentiment and policy
    market_weight = ANALYSIS_CONFIG['market_weight']
    policy_weight = ANALYSIS_CONFIG['policy_weight']
    combined_sentiment = sentiment_score * market_weight + policy_score * policy_weight

    if combined_sentiment > 0.1 and avg_market_change > 1:
        recommendation = "STRONG BUY"
    elif combined_sentiment > 0.05 and avg_market_change > 0:
        recommendation = "BUY"
    elif combined_sentiment > -0.05 and avg_market_change > -1:
        recommendation = "HOLD"
    elif combined_sentiment > -0.1 and avg_market_change > -2:
        recommendation = "CAUTION"
    else:
        recommendation = "SELL"

    # Add policy influence note
    policy_influence = ""
    if policy_analysis and abs(policy_score) > 0.05:
        if policy_score > 0.1:
            policy_influence = " (Policy Supportive)"
        elif policy_score < -0.1:
            policy_influence = " (Policy Headwinds)"
        else:
            policy_influence = " (Policy Neutral)"

    return {
        "recommendation": recommendation + policy_influence,
        "market_trend": market_trend,
        "average_market_change": avg_market_change,
        "combined_sentiment": combined_sentiment,
        "policy_influence": policy_score
    }
