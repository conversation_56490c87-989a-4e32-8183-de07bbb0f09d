# Tell matplotlib to plot inline
%matplotlib inline

import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import zscore

pd.set_option('future.no_silent_downcasting', True)

# -------------------------------
# 1. CONFIGURATION
# -------------------------------

TICKERS        = ["AAPL", "MSFT", "GOOGL", "AMZN", "META", "NVDA", "TSLA", "JPM", "BAC", "WMT"]
START_DATE     = "2018-01-01"
END_DATE       = "2025-06-01"
MOM_LOOKBACK   = 30      # ~3 months
VOL_LOOKBACK   = 30      # use 63-day rolling vol
TARGET_VOL     = 0.10    # 10 % annualized vol (for vol‐parity sizing)
REBAL_FREQ     = "ME"     # month-end rebalancing
TOP_N          = 5       # pick top-5 by momentum
INITIAL_CAPITAL = 100_000

# -------------------------------
# 2. DOWNLOAD PRICE DATA
# -------------------------------

# Download data with auto_adjust=True to get adjusted prices
price_data = yf.download(TICKERS, start=START_DATE, end=END_DATE, auto_adjust=True)["Close"].dropna()

# Forward-fill any missing business days, ensure we have a clean B-day index
price_data = price_data.asfreq("B").ffill()
price_data.head()

# -------------------------------
# 3. COMPUTE DAILY RETURNS & INDICATORS
# -------------------------------

# 3.1 Simple daily returns
rets = price_data.pct_change().dropna()

# 3.2 Rolling volatility (annualized) per ticker
rolling_vol = rets.rolling(window=VOL_LOOKBACK).std() * np.sqrt(252)

# 3.3 3-month momentum: (price_t / price_{t-63}) - 1
mom = price_data.pct_change(periods=MOM_LOOKBACK)

# Peek at the momentum/vol DataFrames
print("Momentum snapshot:\n", mom.iloc[-5:])
print("\nRolling vol snapshot:\n", rolling_vol.iloc[-5:])

# -------------------------------
# 4. BUILD A “REBALANCE SCHEDULE”
# -------------------------------

# Find the last available trading date of each calendar month
month_ends = price_data.resample("ME").apply(lambda x: x.index[-1]).index

# Display the first few month-end dates so you know they make sense
month_ends[:8]

# -------------------------------
# 5. GENERATE MONTHLY WEIGHTS
# -------------------------------

# Create an empty DataFrame filled with zeros
weights = pd.DataFrame(0.0, index=price_data.index, columns=TICKERS)
weights.head()

# Add debug prints to track the weight calculation process
print(f"Number of rebalance dates: {len(month_ends)}")
print(f"First few rebalance dates: {list(month_ends[:5])}")

for dt in month_ends:
    if dt not in price_data.index:
        # Sometimes the resampled month-end is a weekend or holiday
        print(f"Skipping {dt} - not in price_data index")
        continue

    # 5.1 Get momentum for each ticker as of dt
    mom_dt = mom.loc[dt].dropna()
    
    # Debug print
    print(f"\nRebalance date: {dt}")
    print(f"Available momentum values: {len(mom_dt)}")
    if len(mom_dt) > 0:
        print(f"Sample momentum values: {mom_dt.head(3)}")

    # 5.2 Filter out tickers lacking a valid rolling vol or NaN momentum
    valid = (rolling_vol.loc[dt] > 0) & (~mom_dt.isna())
    candidates = mom_dt[valid].nlargest(TOP_N).index.tolist()
    
    # Debug print
    print(f"Valid candidates: {len(candidates)}")
    print(f"Selected tickers: {candidates}")

    # Skip if no valid candidates
    if not candidates:
        print("No valid candidates for this rebalance date, skipping")
        continue

    # 5.3 Compute inverse-vol weights among your top N tickers
    vols = rolling_vol.loc[dt, candidates]
    print(f"Volatilities for selected tickers: {vols}")
    
    inv_vols = 1.0 / vols
    norm_weights = inv_vols / inv_vols.sum()
    
    # Debug print
    print(f"Normalized weights: {norm_weights}")

    # 5.4 Determine date range until next rebalance
    future_month_ends = month_ends[month_ends > dt]
    end_date = future_month_ends.min() if len(future_month_ends) > 0 else price_data.index[-1]
    
    # Debug print
    print(f"Applying weights from {dt} to {end_date}")

    # 5.5 Assign those normalized weights from dt (inclusive) up to end_date (exclusive)
    mask = (price_data.index >= dt) & (price_data.index < end_date)
    for ticker in candidates:
        weights.loc[mask, ticker] = norm_weights.get(ticker, 0.0)

# Fill forward for any days in between rebalances
weights = weights.ffill().fillna(0.0).infer_objects(copy=False)

# Add diagnostic checks after calculating weights
print("\nWeight statistics:")
print(f"Sum of weights (last day): {weights.iloc[-1].sum()}")
print(f"Max weight value: {weights.max().max()}")
print(f"Number of non-zero weights: {(weights > 0).sum().sum()}")

weights.tail()

for dt in month_ends:
    if dt not in price_data.index:
        # Sometimes the resampled month-end is a weekend or holiday —
        # ensure dt is an actual index in price_data
        continue

    # 5.1 Get momentum for each ticker as of dt
    mom_dt = mom.loc[dt].dropna()

    # 5.2 Filter out tickers lacking a valid rolling vol or NaN momentum
    valid = (rolling_vol.loc[dt] > 0) & (~mom_dt.isna())
    candidates = mom_dt[valid].nlargest(TOP_N).index.tolist()

    # 5.3 Compute inverse-vol weights among your top N tickers
    vols = rolling_vol.loc[dt, candidates]
    inv_vols = 1.0 / vols
    norm_weights = inv_vols / inv_vols.sum()

    # 5.4 Determine date range until next rebalance
    future_month_ends = month_ends[month_ends > dt]
    end_date = future_month_ends.min() if len(future_month_ends) > 0 else price_data.index[-1]

    # 5.5 Assign those normalized weights from dt (inclusive) up to end_date (exclusive)
    mask = (price_data.index >= dt) & (price_data.index < end_date)
    for ticker in TICKERS:
        weights.loc[mask, ticker] = norm_weights.get(ticker, 0.0)

# Fill forward for any days in between rebalances
weights = weights.ffill().fillna(0.0).infer_objects(copy=False)
weights.tail()

# -------------------------------
# 6. SIMULATE PORTFOLIO P&L
# -------------------------------

# 6.1 Shift weights by one day (we assume weights “lock in” at the close of day t,
#     and returns are realized on day t+1).
shifted_weights = weights.shift(1).fillna(0.0)

# 6.2 Compute the daily portfolio return as the dot product of shifted_weights and rets
daily_portfolio_ret = (shifted_weights * rets).sum(axis=1)

# 6.3 Build the equity curve
equity = (1 + daily_portfolio_ret).cumprod() * INITIAL_CAPITAL

# Quick sanity‐check: show the last few lines of each series
pd.DataFrame({
    "DailyPortRet": daily_portfolio_ret.tail(),
    "Equity": equity.tail()
})

# 6.4 Cumulative return
cum_return = equity.iloc[-1] / INITIAL_CAPITAL - 1

# 6.5 Max drawdown
running_max = equity.cummax()
drawdowns = (equity - running_max) / running_max
max_drawdown = drawdowns.min()

# 6.6 Annualized Sharpe (assume rf ≈ 0)
std_dev = daily_portfolio_ret.std()
if std_dev > 0:
    ann_sharpe = (daily_portfolio_ret.mean() / std_dev) * np.sqrt(252)
else:
    ann_sharpe = np.nan
    print("Warning: Standard deviation of returns is zero, Sharpe ratio cannot be calculated")

print("----- Strategy Performance -----")
print(f"Cumulative Return:    {cum_return * 100:.2f}%")
print(f"Max Drawdown:         {max_drawdown * 100:.2f}%")
print(f"Annualized Sharpe:    {ann_sharpe:.2f}")

# -------------------------------
# 7. CALCULATE SHARES AND POSITIONS
# -------------------------------

# Create a DataFrame to store the number of shares for each ticker
shares = pd.DataFrame(index=month_ends, columns=TICKERS)

# Calculate shares at each rebalance date
for dt in month_ends:
    if dt not in price_data.index:
        continue
        
    # Get the portfolio value on this date
    if dt == price_data.index[0]:
        # For the first date, use initial capital
        portfolio_value = INITIAL_CAPITAL
    else:
        # For subsequent dates, use the actual portfolio value
        portfolio_value = equity.loc[dt]
    
    # Get the weights for this date
    current_weights = weights.loc[dt]
    
    # Get the prices for this date
    current_prices = price_data.loc[dt]
    
    # Calculate the dollar amount to invest in each ticker
    dollar_amounts = current_weights * portfolio_value
    
    # Calculate the number of shares (rounded down to whole shares)
    for ticker in TICKERS:
        if current_prices[ticker] > 0 and dollar_amounts[ticker] > 0:
            shares.loc[dt, ticker] = int(dollar_amounts[ticker] / current_prices[ticker])

# Reindex to include all dates and forward fill
shares = shares.reindex(price_data.index, method='ffill')
# Fill NaN values with 0
shares = shares.fillna(0).infer_objects(copy=False).astype(int)
# Convert to integer type
shares = shares.astype(int)

# Display the shares at each rebalance date
print("\n----- Shares at Each Rebalance Date -----")
rebalance_shares = shares.loc[month_ends[month_ends.isin(shares.index)]]
print(rebalance_shares)

# Display the final portfolio composition
print("\n----- Final Portfolio Composition -----")
final_shares = shares.iloc[-1]
final_prices = price_data.iloc[-1]
final_values = final_shares * final_prices

# Create a summary DataFrame
portfolio_summary = pd.DataFrame({
    'Ticker': TICKERS,
    'Shares': final_shares.values,
    'Price': final_prices.values,
    'Value': final_values.values,
    'Weight': final_values / final_values.sum()
})

# Filter out zero positions
portfolio_summary = portfolio_summary[portfolio_summary['Shares'] > 0]
print(portfolio_summary)

# Calculate cash remaining (due to rounding down to whole shares)
total_invested = final_values.sum()
cash_remaining = equity.iloc[-1] - total_invested
print(f"\nTotal Portfolio Value: ${equity.iloc[-1]:,.2f}")
print(f"Total Invested in Stocks: ${total_invested:,.2f}")
print(f"Cash Remaining: ${cash_remaining:,.2f}")

# --------------------------------------
# 8. PLOT RESULTS
# --------------------------------------

# 8.1 Plot the equity curve
plt.figure(figsize=(12, 6))
plt.plot(equity, label="Mom+Vol-Parity Portfolio", linewidth=2, color="navy")

# Calculate a buy-and-hold portfolio (equal weight at start)
equal_weight = INITIAL_CAPITAL / len(TICKERS)
buy_hold_values = pd.DataFrame()
for ticker in TICKERS:
    buy_hold_values[ticker] = price_data[ticker] * (equal_weight / price_data[ticker].iloc[0])
buy_hold_equity = buy_hold_values.sum(axis=1)

# Add buy-and-hold line for comparison
plt.plot(buy_hold_equity, label="Equal-Weight Buy & Hold", linewidth=1.5, color="gray", linestyle="--")

# Add a title and labels
plt.title("Equity Curve: Momentum + Vol-Parity Strategy vs Buy & Hold")
plt.xlabel("Date")
plt.ylabel("Portfolio Value (USD)")
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.show()

# 8.2 Plot share counts over time for each ticker
plt.figure(figsize=(14, 8))

# Get tickers that had non-zero shares at some point
active_tickers = [ticker for ticker in TICKERS if shares[ticker].max() > 0]

# Plot each ticker's share count
for ticker in active_tickers:
    plt.plot(shares.index, shares[ticker], label=ticker, linewidth=2)

plt.title("Share Counts Over Time", fontsize=16)
plt.xlabel("Date", fontsize=12)
plt.ylabel("Number of Shares", fontsize=12)
plt.legend(loc="upper left")
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

# 8.3 Create a stacked area chart of portfolio composition by value
plt.figure(figsize=(14, 8))

# Calculate the dollar value of each position over time
position_values = pd.DataFrame(index=shares.index, columns=TICKERS)
for ticker in TICKERS:
    position_values[ticker] = shares[ticker] * price_data[ticker]

# Calculate cash component (difference between total equity and sum of stock positions)
stock_value_total = position_values.sum(axis=1)
position_values['Cash'] = equity - stock_value_total

# Filter to include only active tickers and cash
active_columns = active_tickers + ['Cash']
position_values = position_values[active_columns]

# Create the stacked area chart
plt.stackplot(position_values.index, 
              [position_values[col] for col in position_values.columns],
              labels=position_values.columns, 
              alpha=0.8)

plt.title("Portfolio Composition Over Time (by Value)", fontsize=16)
plt.xlabel("Date", fontsize=12)
plt.ylabel("Position Value (USD)", fontsize=12)
plt.legend(loc="upper left")
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

# 8.4 Create a chart showing portfolio weights over time
plt.figure(figsize=(14, 8))

# Calculate weights
portfolio_weights = position_values.copy()
for date in portfolio_weights.index:
    portfolio_weights.loc[date] = position_values.loc[date] / equity.loc[date]

# Create the stacked area chart of weights
plt.stackplot(portfolio_weights.index, 
              [portfolio_weights[col] for col in portfolio_weights.columns],
              labels=portfolio_weights.columns, 
              alpha=0.8)

plt.title("Portfolio Weights Over Time", fontsize=16)
plt.xlabel("Date", fontsize=12)
plt.ylabel("Weight (% of Portfolio)", fontsize=12)
plt.legend(loc="upper left")
plt.grid(True, alpha=0.3)
plt.axhline(y=1.0, color='black', linestyle='--', alpha=0.3)  # Line at 100%
plt.tight_layout()
plt.show()

# 8.5 Create a chart showing when shares were bought or sold
plt.figure(figsize=(14, 8))

# Calculate daily changes in share counts
share_changes = shares.diff().fillna(0)

# Plot buy transactions (positive changes)
for ticker in active_tickers:
    buy_dates = share_changes.index[share_changes[ticker] > 0]
    if len(buy_dates) > 0:  # Only plot if there are buy transactions
        buy_amounts = share_changes.loc[buy_dates, ticker]
        plt.stem(buy_dates, buy_amounts, linefmt='g-', markerfmt='go', basefmt=' ', 
                label=f"{ticker} Buys" if ticker == active_tickers[0] else "")
        
        # Add annotations for significant buys
        for date, amount in zip(buy_dates, buy_amounts):
            if amount > shares[ticker].max() * 0.1:  # Only annotate significant buys (>10% of max)
                plt.annotate(f"+{int(amount)} {ticker}", 
                            xy=(date, amount),
                            xytext=(0, 10),
                            textcoords="offset points",
                            ha='center',
                            fontsize=8)

# Plot sell transactions (negative changes)
for ticker in active_tickers:
    sell_dates = share_changes.index[share_changes[ticker] < 0]
    if len(sell_dates) > 0:  # Only plot if there are sell transactions
        sell_amounts = -share_changes.loc[sell_dates, ticker]  # Make positive for plotting
        plt.stem(sell_dates, sell_amounts, linefmt='r-', markerfmt='ro', basefmt=' ',
                label=f"{ticker} Sells" if ticker == active_tickers[0] else "")
        
        # Add annotations for significant sells
        for date, amount in zip(sell_dates, sell_amounts):
            if amount > shares[ticker].max() * 0.1:  # Only annotate significant sells (>10% of max)
                plt.annotate(f"-{int(amount)} {ticker}", 
                            xy=(date, amount),
                            xytext=(0, 10),
                            textcoords="offset points",
                            ha='center',
                            fontsize=8)

# Only add legend if there are both buys and sells
has_buys = any(share_changes[ticker].max() > 0 for ticker in active_tickers)
has_sells = any(share_changes[ticker].min() < 0 for ticker in active_tickers)

if has_buys and has_sells:
    plt.legend(["Buys", "Sells"])
elif has_buys:
    plt.legend(["Buys"])
elif has_sells:
    plt.legend(["Sells"])

plt.title("Share Purchases and Sales Over Time", fontsize=16)
plt.xlabel("Date", fontsize=12)
plt.ylabel("Number of Shares", fontsize=12)
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

# 8.6 Plot portfolio value vs stock values
plt.figure(figsize=(14, 8))

# Plot the portfolio equity
plt.plot(equity, label="Portfolio Value", linewidth=2.5, color="navy")

# Plot individual stock contributions
for ticker in active_tickers:
    stock_value = shares[ticker] * price_data[ticker]
    plt.plot(stock_value, label=f"{ticker} Value", linewidth=1.5, alpha=0.7)

plt.title("Portfolio Value vs Individual Stock Values", fontsize=16)
plt.xlabel("Date", fontsize=12)
plt.ylabel("Value (USD)", fontsize=12)
plt.legend(loc="upper left")
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

# 8.7 Plot cumulative returns comparison
plt.figure(figsize=(14, 8))

# Calculate cumulative returns
portfolio_cum_return = equity / INITIAL_CAPITAL
stock_cum_returns = pd.DataFrame(index=price_data.index)

for ticker in TICKERS:
    stock_cum_returns[ticker] = price_data[ticker] / price_data[ticker].iloc[0]

# Plot portfolio cumulative return
plt.plot(portfolio_cum_return, label="Portfolio", linewidth=2.5, color="navy")

# Plot individual stock cumulative returns
for ticker in active_tickers:
    plt.plot(stock_cum_returns[ticker], label=ticker, linewidth=1.5, alpha=0.7)

plt.title("Cumulative Returns: Portfolio vs Individual Stocks", fontsize=16)
plt.xlabel("Date", fontsize=12)
plt.ylabel("Cumulative Return (1.0 = 100%)", fontsize=12)
plt.legend(loc="upper left")
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

# -------------------------------
# 9. COMPARE MULTIPLE TICKER GROUPS
# -------------------------------

def run_backtest(tickers, start_date=START_DATE, end_date=END_DATE, 
                mom_lookback=MOM_LOOKBACK, vol_lookback=VOL_LOOKBACK, 
                target_vol=TARGET_VOL, rebal_freq=REBAL_FREQ, 
                top_n=TOP_N, initial_capital=INITIAL_CAPITAL):
    """
    Run the momentum + volatility parity strategy on a given set of tickers.
    Returns a DataFrame with the equity curve and performance metrics.
    """
    try:
        # Download price data
        price_data = yf.download(tickers, start=start_date, end=end_date, auto_adjust=True)["Close"]
        
        # Check if we have data
        if price_data.empty:
            print(f"  Warning: No data found for tickers {tickers}")
            return None
            
        # Drop columns with all NaN values
        price_data = price_data.dropna(axis=1, how='all')
        
        # Check if we have any tickers left
        if price_data.empty or len(price_data.columns) == 0:
            print(f"  Warning: No valid data found for tickers {tickers}")
            return None
            
        # Forward-fill any missing business days
        price_data = price_data.asfreq("B").ffill()
        
        # Compute daily returns & indicators
        rets = price_data.pct_change().dropna()
        rolling_vol = rets.rolling(window=vol_lookback).std() * np.sqrt(252)
        mom = price_data.pct_change(periods=mom_lookback)
        
        # Build rebalance schedule
        month_ends = price_data.resample(rebal_freq).apply(lambda x: x.index[-1]).index
        
        # Generate weights
        weights = pd.DataFrame(0.0, index=price_data.index, columns=price_data.columns)
        
        # Track if we ever found valid candidates
        found_valid_candidates = False
        
        for dt in month_ends:
            if dt not in price_data.index:
                continue
                
            # Get momentum for each ticker as of dt
            mom_dt = mom.loc[dt].dropna()
            
            # Filter out tickers lacking a valid rolling vol or NaN momentum
            valid = (rolling_vol.loc[dt] > 0) & (~mom_dt.isna())
            
            # Check if we have any valid candidates
            if valid.sum() == 0:
                continue
                
            candidates = mom_dt[valid].nlargest(min(top_n, len(mom_dt[valid]))).index.tolist()
            
            if not candidates:
                continue
                
            # We found at least one valid rebalance
            found_valid_candidates = True
                
            # Compute inverse-vol weights among top N tickers
            vols = rolling_vol.loc[dt, candidates]
            inv_vols = 1.0 / vols
            norm_weights = inv_vols / inv_vols.sum()
            
            # Determine date range until next rebalance
            future_month_ends = month_ends[month_ends > dt]
            end_date_rebal = future_month_ends.min() if len(future_month_ends) > 0 else price_data.index[-1]
            
            # Assign weights
            mask = (price_data.index >= dt) & (price_data.index < end_date_rebal)
            for ticker in candidates:
                weights.loc[mask, ticker] = norm_weights.get(ticker, 0.0)
        
        # If we never found valid candidates, return None
        if not found_valid_candidates:
            print(f"  Warning: No valid rebalancing opportunities found for {tickers}")
            return None
        
        # Fill forward for any days in between rebalances
        weights = weights.ffill().fillna(0.0)
        
        # Simulate portfolio P&L
        shifted_weights = weights.shift(1).fillna(0.0)
        daily_portfolio_ret = (shifted_weights * rets).sum(axis=1)
        equity = (1 + daily_portfolio_ret).cumprod() * initial_capital
        
        # Check if equity series is valid
        if len(equity) == 0:
            print(f"  Warning: No valid equity curve generated for {tickers}")
            return None
        
        # Calculate performance metrics
        cum_return = equity.iloc[-1] / initial_capital - 1
        running_max = equity.cummax()
        drawdowns = (equity - running_max) / running_max
        max_drawdown = drawdowns.min()
        
        # Calculate Sharpe ratio safely
        if daily_portfolio_ret.std() > 0:
            ann_sharpe = (daily_portfolio_ret.mean() / daily_portfolio_ret.std()) * np.sqrt(252)
        else:
            ann_sharpe = 0
            print(f"  Warning: Zero standard deviation for {tickers}, setting Sharpe to 0")
        
        return {
            'equity': equity,
            'cum_return': cum_return,
            'max_drawdown': max_drawdown,
            'ann_sharpe': ann_sharpe,
            'weights': weights,
            'price_data': price_data
        }
        
    except Exception as e:
        print(f"  Error running backtest for {tickers}: {str(e)}")
        return None

# Run backtest for each group
results = {}
for group_name, tickers in ticker_groups.items():
    print(f"Running backtest for {group_name}...")
    result = run_backtest(tickers)
    
    if result is not None:
        results[group_name] = result
        print(f"  Return: {result['cum_return']*100:.2f}%")
        print(f"  Max Drawdown: {result['max_drawdown']*100:.2f}%")
        print(f"  Sharpe Ratio: {result['ann_sharpe']:.2f}")
    else:
        print(f"  Skipping {group_name} due to insufficient data")
    
    print()

# Only proceed with plotting if we have results
if not results:
    print("No valid results to plot. Please check your ticker groups and date ranges.")
else:
    # Plot equity curves for all groups
    plt.figure(figsize=(16, 10))

    for group_name, result in results.items():
        plt.plot(result['equity'], label=f"{group_name} ({result['cum_return']*100:.1f}%)")

    plt.title("Momentum + Vol-Parity Strategy: Performance Across Different Ticker Groups", fontsize=16)
    plt.xlabel("Date", fontsize=12)
    plt.ylabel("Portfolio Value ($)", fontsize=12)
    plt.legend(loc="upper left")
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

    # Create a summary table
    summary_data = []
    for group_name, result in results.items():
        summary_data.append({
            'Group': group_name,
            'Return (%)': result['cum_return'] * 100,
            'Max Drawdown (%)': result['max_drawdown'] * 100,
            'Sharpe Ratio': result['ann_sharpe'],
            'Final Value ($)': result['equity'].iloc[-1]
        })

    summary_df = pd.DataFrame(summary_data)
    summary_df = summary_df.sort_values('Return (%)', ascending=False)
    summary_df = summary_df.set_index('Group')
    summary_df = summary_df.round(2)

    print("Performance Summary (Sorted by Return):")
    print(summary_df)

    # Plot drawdowns for all groups
    plt.figure(figsize=(16, 10))

    for group_name, result in results.items():
        equity = result['equity']
        running_max = equity.cummax()
        drawdowns = (equity - running_max) / running_max
        plt.plot(drawdowns, label=f"{group_name} (Max DD: {result['max_drawdown']*100:.1f}%)")

    plt.title("Drawdowns Across Different Ticker Groups", fontsize=16)
    plt.xlabel("Date", fontsize=12)
    plt.ylabel("Drawdown (%)", fontsize=12)
    plt.legend(loc="lower left")
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

# -------------------------------
# 10. COMPARE MULTIPLE PORTFOLIOS
# -------------------------------

def run_portfolio_simulation(ticker_groups, start_date=START_DATE, end_date=END_DATE, 
                            mom_lookback=MOM_LOOKBACK, vol_lookback=VOL_LOOKBACK, 
                            target_vol=TARGET_VOL, rebal_freq=REBAL_FREQ, 
                            top_n=TOP_N, initial_capital=INITIAL_CAPITAL):
    """
    Run the momentum + volatility parity strategy on multiple portfolios simultaneously.
    Each portfolio is allocated to a different ticker group.
    
    Returns a dictionary of portfolio results.
    """
    portfolios = {}
    
    # Run backtest for each group
    for group_name, tickers in ticker_groups.items():
        print(f"Creating portfolio for {group_name}...")
        result = run_backtest(tickers, start_date, end_date, mom_lookback, 
                             vol_lookback, target_vol, rebal_freq, top_n, initial_capital)
        
        if result is not None:
            portfolios[group_name] = result
            print(f"  Return: {result['cum_return']*100:.2f}%")
            print(f"  Max Drawdown: {result['max_drawdown']*100:.2f}%")
            print(f"  Sharpe Ratio: {result['ann_sharpe']:.2f}")
        else:
            print(f"  Skipping {group_name} due to insufficient data")
        
        print()
    
    return portfolios

# Define different ticker groups
ticker_groups = {
    'Tech Giants': ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'NVDA'],
    'Dow Components': ['AAPL', 'MSFT', 'JPM', 'V', 'PG', 'HD', 'UNH', 'DIS', 'MCD', 'CRM'],
    'S&P Sectors': ['XLK', 'XLF', 'XLV', 'XLC', 'XLY', 'XLP', 'XLI', 'XLU', 'XLB', 'XLE', 'XLRE'],
    'Global ETFs': ['SPY', 'QQQ', 'EFA', 'EEM', 'VGK', 'VPL', 'VWO', 'IWM', 'GLD', 'TLT'],
    'Dividend Stocks': ['JNJ', 'PG', 'KO', 'PEP', 'VZ', 'T', 'MO', 'PM', 'XOM', 'CVX'],
    'Value Stocks': ['BRK-B', 'JPM', 'JNJ', 'PG', 'UNH', 'HD', 'V', 'MA', 'DIS', 'VZ']
}

# Run the portfolio simulation
portfolios = run_portfolio_simulation(ticker_groups, initial_capital=INITIAL_CAPITAL)

# Only proceed with analysis if we have results
if not portfolios:
    print("No valid portfolios to analyze. Please check your ticker groups and date ranges.")
else:
    # 1. Plot equity curves for all portfolios
    plt.figure(figsize=(16, 10))

    for group_name, result in portfolios.items():
        plt.plot(result['equity'], label=f"{group_name} ({result['cum_return']*100:.1f}%)")

    plt.title("Portfolio Performance Comparison", fontsize=16)
    plt.xlabel("Date", fontsize=12)
    plt.ylabel("Portfolio Value ($)", fontsize=12)
    plt.legend(loc="upper left")
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

    # 2. Create a summary table
    summary_data = []
    for group_name, result in portfolios.items():
        # Calculate additional metrics
        returns = result['equity'].pct_change().dropna()
        annual_return = (1 + result['cum_return']) ** (252 / len(returns)) - 1
        volatility = returns.std() * np.sqrt(252)
        max_dd = result['max_drawdown']
        calmar = annual_return / abs(max_dd) if max_dd != 0 else float('inf')
        
        summary_data.append({
            'Portfolio': group_name,
            'Total Return (%)': result['cum_return'] * 100,
            'Annual Return (%)': annual_return * 100,
            'Volatility (%)': volatility * 100,
            'Max Drawdown (%)': max_dd * 100,
            'Sharpe Ratio': result['ann_sharpe'],
            'Calmar Ratio': calmar,
            'Final Value ($)': result['equity'].iloc[-1]
        })

    summary_df = pd.DataFrame(summary_data)
    summary_df = summary_df.sort_values('Total Return (%)', ascending=False)
    summary_df = summary_df.set_index('Portfolio')
    summary_df = summary_df.round(2)

    print("Portfolio Performance Summary (Sorted by Total Return):")
    print(summary_df)

    # 3. Plot drawdowns for all portfolios
    plt.figure(figsize=(16, 10))

    for group_name, result in portfolios.items():
        equity = result['equity']
        running_max = equity.cummax()
        drawdowns = (equity - running_max) / running_max
        plt.plot(drawdowns, label=f"{group_name} (Max DD: {result['max_drawdown']*100:.1f}%)")

    plt.title("Portfolio Drawdowns Comparison", fontsize=16)
    plt.xlabel("Date", fontsize=12)
    plt.ylabel("Drawdown (%)", fontsize=12)
    plt.legend(loc="lower left")
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

    # 4. Plot rolling 30-day returns
    plt.figure(figsize=(16, 10))
    
    for group_name, result in portfolios.items():
        rolling_returns = result['equity'].pct_change(30).dropna() * 100  # 30-day rolling returns in percent
        plt.plot(rolling_returns, label=f"{group_name}")
    
    plt.title("30-Day Rolling Returns", fontsize=16)
    plt.xlabel("Date", fontsize=12)
    plt.ylabel("30-Day Return (%)", fontsize=12)
    plt.legend(loc="upper left")
    plt.grid(True, alpha=0.3)
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    plt.tight_layout()
    plt.show()

    # 5. Plot rolling 60-day volatility
    plt.figure(figsize=(16, 10))
    
    for group_name, result in portfolios.items():
        rolling_vol = result['equity'].pct_change().rolling(window=60).std() * np.sqrt(252) * 100  # Annualized
        plt.plot(rolling_vol, label=f"{group_name}")
    
    plt.title("60-Day Rolling Volatility (Annualized)", fontsize=16)
    plt.xlabel("Date", fontsize=12)
    plt.ylabel("Volatility (%)", fontsize=12)
    plt.legend(loc="upper left")
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

    # 6. Create a correlation matrix of portfolio returns
    returns_df = pd.DataFrame()
    
    for group_name, result in portfolios.items():
        returns_df[group_name] = result['equity'].pct_change().dropna()
    
    corr_matrix = returns_df.corr()
    
    plt.figure(figsize=(12, 10))
    sns.heatmap(corr_matrix, annot=True, cmap="coolwarm", fmt=".2f", linewidths=.5)
    plt.title("Correlation Matrix of Portfolio Returns", fontsize=16)
    plt.tight_layout()
    plt.show()

    # 7. Plot the number of holdings over time for each portfolio
    plt.figure(figsize=(16, 10))
    
    for group_name, result in portfolios.items():
        # Count non-zero weights for each date
        holdings_count = (result['weights'] > 0).sum(axis=1)
        plt.plot(holdings_count, label=f"{group_name}")
    
    plt.title("Number of Holdings Over Time", fontsize=16)
    plt.xlabel("Date", fontsize=12)
    plt.ylabel("Number of Holdings", fontsize=12)
    plt.legend(loc="upper left")
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

    # 8. Create a multi-portfolio dashboard - Fixed version to avoid tight_layout warning
    fig = plt.figure(figsize=(20, 15))
    gs = fig.add_gridspec(3, 2, hspace=0.4, wspace=0.3)  # Increased spacing between subplots

    # Equity curves
    ax1 = fig.add_subplot(gs[0, :])
    for group_name, result in portfolios.items():
        ax1.plot(result['equity'], label=f"{group_name}")
    ax1.set_title("Portfolio Values", fontsize=14)
    ax1.set_ylabel("Value ($)", fontsize=12)
    ax1.legend(loc="upper left")
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(axis='x', labelrotation=45)  # Rotate x-axis labels

    # Drawdowns
    ax2 = fig.add_subplot(gs[1, 0])
    for group_name, result in portfolios.items():
        equity = result['equity']
        running_max = equity.cummax()
        drawdowns = (equity - running_max) / running_max
        ax2.plot(drawdowns, label=f"{group_name}")
    ax2.set_title("Drawdowns", fontsize=14)
    ax2.set_ylabel("Drawdown (%)", fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.tick_params(axis='x', labelrotation=45)

    # Rolling returns
    ax3 = fig.add_subplot(gs[1, 1])
    for group_name, result in portfolios.items():
        rolling_returns = result['equity'].pct_change(30).dropna() * 100
        ax3.plot(rolling_returns, label=f"{group_name}")
    ax3.set_title("30-Day Rolling Returns", fontsize=14)
    ax3.set_ylabel("Return (%)", fontsize=12)
    ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax3.grid(True, alpha=0.3)
    ax3.tick_params(axis='x', labelrotation=45)

    # Replace the "Number of Holdings" plot with a more useful "Portfolio Turnover" plot
    ax4 = fig.add_subplot(gs[2, 0])
    for group_name, result in portfolios.items():
        # Calculate portfolio turnover (changes in weights)
        weights = result['weights']
        # Calculate absolute weight changes between rebalance periods
        weight_changes = weights.diff().abs().sum(axis=1).fillna(0)
        # Plot as a rolling 3-period sum to smooth and show turnover rate
        turnover = weight_changes.rolling(window=3).sum()
        ax4.plot(turnover, label=f"{group_name}")
    
    ax4.set_title("Portfolio Turnover (3-Period Rolling Sum)", fontsize=14)
    ax4.set_ylabel("Turnover Rate", fontsize=12)
    ax4.grid(True, alpha=0.3)
    ax4.tick_params(axis='x', labelrotation=45)

    # Rolling volatility
    ax5 = fig.add_subplot(gs[2, 1])
    for group_name, result in portfolios.items():
        rolling_vol = result['equity'].pct_change().rolling(window=60).std() * np.sqrt(252) * 100
        ax5.plot(rolling_vol, label=f"{group_name}")
    ax5.set_title("60-Day Rolling Volatility", fontsize=14)
    ax5.set_ylabel("Volatility (%)", fontsize=12)
    ax5.grid(True, alpha=0.3)
    ax5.tick_params(axis='x', labelrotation=45)

    # Adjust the layout manually instead of using tight_layout
    plt.subplots_adjust(top=0.95, bottom=0.1, left=0.08, right=0.95, hspace=0.4, wspace=0.3)
    plt.show()

    # 9. Create a combined portfolio (equal weight to each portfolio)
    if len(portfolios) > 1:
        print("\nCreating a combined portfolio (equal weight to each strategy)...")
        
        # Get the common date range
        common_dates = None
        for result in portfolios.values():
            if common_dates is None:
                common_dates = set(result['equity'].index)
            else:
                common_dates = common_dates.intersection(set(result['equity'].index))
        
        common_dates = sorted(common_dates)
        
        # Create a DataFrame with all portfolio values
        all_portfolios = pd.DataFrame(index=common_dates)
        for group_name, result in portfolios.items():
            all_portfolios[group_name] = result['equity'].reindex(common_dates)
        
        # Calculate the combined portfolio (equal weight)
        initial_allocation = INITIAL_CAPITAL / len(portfolios)
        combined_portfolio = pd.Series(0.0, index=common_dates)
        
        for group_name in portfolios.keys():
            normalized_equity = all_portfolios[group_name] / all_portfolios[group_name].iloc[0] * initial_allocation
            combined_portfolio += normalized_equity
        
        # Calculate metrics for the combined portfolio
        combined_returns = combined_portfolio.pct_change().dropna()
        combined_cum_return = combined_portfolio.iloc[-1] / INITIAL_CAPITAL - 1
        combined_annual_return = (1 + combined_cum_return) ** (252 / len(combined_returns)) - 1
        combined_volatility = combined_returns.std() * np.sqrt(252)
        combined_max_dd = (combined_portfolio / combined_portfolio.cummax() - 1).min()
        combined_sharpe = (combined_annual_return / combined_volatility) if combined_volatility > 0 else 0
        combined_calmar = combined_annual_return / abs(combined_max_dd) if combined_max_dd != 0 else float('inf')
        
        # Add combined portfolio to summary
        summary_df.loc['Combined Portfolio'] = [
            combined_cum_return * 100,
            combined_annual_return * 100,
            combined_volatility * 100,
            combined_max_dd * 100,
            combined_sharpe,
            combined_calmar,
            combined_portfolio.iloc[-1]
        ]
        
        print("Updated Performance Summary (including Combined Portfolio):")
        print(summary_df)
        
        # Plot the combined portfolio against individual portfolios
        plt.figure(figsize=(16, 10))
        
        for group_name, result in portfolios.items():
            plt.plot(result['equity'], label=f"{group_name}", alpha=0.5)
        
        plt.plot(combined_portfolio, label="Combined Portfolio", linewidth=3, color='black')
        
        plt.title("Combined Portfolio vs Individual Portfolios", fontsize=16)
        plt.xlabel("Date", fontsize=12)
        plt.ylabel("Portfolio Value ($)", fontsize=12)
        plt.legend(loc="upper left")
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()